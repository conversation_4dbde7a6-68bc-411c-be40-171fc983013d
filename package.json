{"name": "tradai-signal-generator", "version": "1.0.0", "description": "AI-powered trading signal generator with React frontend and Node.js backend", "main": "src/index.js", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "vercel-prebuild": "node vercel-deploy.js", "vercel-build": "npm run vercel-prebuild && next build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:signal": "node tests/signalEngineTest.js", "test:backtest": "node tests/backtestRunnerTest.js", "test:comprehensive": "node test-comprehensive-analysis.js", "test:simple": "node tests/simpleSignalTest.js", "test:diagnostics": "node tests/systemDiagnostics.js", "test:all": "npm run test:diagnostics && npm run test:simple && npm run test:comprehensive", "test:ultimate": "node tests/ultimateSystemTest.js", "test:production": "node tests/productionSystemTest.js", "test:otc-real-data": "node tests/otcRealDataTest.js", "test:qxbroker-otc": "node tests/qxBrokerOtcTest.js", "validate:real-data": "node validate-real-data.js", "setup:ultimate": "node setup-ultimate.js", "setup:production": "node scripts/setup-production-system.js", "setup:browser-automation": "node scripts/setup-browser-automation.js", "setup:qxbroker-otc": "node scripts/setup-qxbroker-otc.js", "health-check": "node -e \"require('./pages/api/system-health.js').healthCheck().then(console.log)\"", "qxbroker-health": "node -e \"require('./pages/api/qxbroker-otc-signal.js').healthCheck().then(console.log)\"", "ultra-precision-health": "node -e \"require('./pages/api/ultra-precision-signal.ts').healthCheck().then(console.log)\"", "ultimate": "node src/ultimate-main.js", "ultimate:dev": "NODE_ENV=development node src/ultimate-main.js", "production": "npm run build && npm run start", "production:dev": "NODE_ENV=production npm run dev", "install:browser-automation": "npm install playwright tesseract.js sharp --save", "qxbroker-otc": "npm run dev && open http://localhost:3000/qxbroker-otc-signals", "setup:ultra-precision": "node scripts/setup-ultra-precision-system.js", "test:ultra-precision": "node tests/ultraPrecisionSignalTest.js", "ultra-precision": "npm run dev && open http://localhost:3000/ultra-precision-signals", "test:enhanced-ocr": "node test-screenshot-analysis.js", "test:enhanced-ocr:specific": "node test-screenshot-analysis.js --specific", "test:enhanced-ocr:demo": "node test-screenshot-analysis.js --demo", "analyze:screenshots": "node test-screenshot-analysis.js", "enhanced-analyzer": "node test-screenshot-analysis.js", "test:usdinr": "node test-usdinr-analysis.js", "test:usdinr:debug": "node test-usdinr-analysis.js --debug", "test:usdinr:capabilities": "node test-usdinr-analysis.js --capabilities", "analyze:usdinr": "node test-usdinr-analysis.js", "debug:usdinr": "node debug-ocr-analysis.js", "test:usdbrl": "node test-usdbrl-analysis.js", "test:usdbrl:debug": "node test-usdbrl-analysis.js --debug", "test:usdbrl:capabilities": "node test-usdbrl-analysis.js --capabilities", "analyze:usdbrl": "node test-usdbrl-analysis.js", "debug:usdbrl": "node debug-usdbrl-ocr.js", "analyze:comprehensive": "node test-comprehensive-analysis.js", "test:ai-pipeline": "node test-comprehensive-analysis.js", "analyze:ai": "node test-comprehensive-analysis.js"}, "dependencies": {"@google-cloud/vision": "^5.3.2", "@google/generative-ai": "^0.24.1", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@tensorflow/tfjs": "^4.22.0", "@tensorflow/tfjs-node": "^4.22.0", "@types/fs-extra": "11.0.4", "@types/lodash": "4.17.7", "@types/node": "20.14.12", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "autoprefixer": "10.4.19", "axios": "^1.11.0", "chrome-aws-lambda": "^10.1.0", "class-variance-authority": "0.7.0", "clsx": "2.1.1", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.21.2", "fabric": "^6.7.1", "file-type": "^21.0.0", "form-data": "^4.0.4", "formidable": "^3.5.4", "framer-motion": "11.3.19", "fs-extra": "11.2.0", "groq-sdk": "0.5.0", "helmet": "^7.2.0", "image-size": "^2.0.2", "jimp": "^1.6.0", "lodash": "4.17.21", "lucide-react": "0.408.0", "mathjs": "^12.2.1", "ml-matrix": "^6.12.1", "ml-regression": "^2.0.0", "moment": "^2.29.4", "multer": "^2.0.2", "next": "14.2.5", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "openai": "^5.10.1", "pixelmatch": "^7.1.0", "playwright": "^1.42.1", "pngjs": "^7.0.0", "postcss": "8.4.40", "puppeteer": "^24.14.0", "puppeteer-core": "^10.1.0", "rate-limiter-flexible": "^2.4.2", "react": "18.3.1", "react-dom": "18.3.1", "sharp": "^0.33.5", "simple-statistics": "^7.8.3", "skia-canvas": "^2.0.2", "tailwind-merge": "^3.3.1", "tailwindcss": "3.4.7", "technicalindicators": "^3.1.0", "tesseract.js": "^6.0.1", "tulind": "^0.8.20", "typescript": "5.5.4", "uuid": "^9.0.1", "winston": "^3.11.0", "ws": "^8.14.2", "yahoo-finance2": "^2.13.3"}, "devDependencies": {"@jest/globals": "^29.7.0", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@types/jest": "^29.5.12", "@types/next": "^8.0.7", "eslint": "8.57.0", "eslint-config-next": "14.2.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "ts-jest": "^29.1.2"}, "engines": {"node": ">=18.0.0"}}