# Ultimate AI Trading Signal System Configuration
# Copy this file to .env and fill in your API keys

# =============================================================================
# MARKET DATA PROVIDERS (Multi-Source Data Fusion)
# =============================================================================

# Primary Data Provider
TWELVE_DATA_API_KEY=your_twelve_data_api_key_here

# Secondary Data Providers (for data fusion and redundancy)
FINNHUB_API_KEY=d1t566pr01qh0t04t32gd1t566pr01qh0t04t330
ALPHA_VANTAGE_API_KEY=B5V6LID8ZMLCB8I
POLYGON_API_KEY=********************************

# =============================================================================
# AI PROVIDERS (Three-Brain Architecture)
# =============================================================================

# Fast AI Providers (Reflex Brain)
GROQ_API_KEY=your_groq_api_key_here
TOGETHER_API_KEY=your_together_api_key_here

# Advanced AI Providers (Analyst Brain)
OPENROUTER_API_KEY=your_openrouter_api_key_here
FIREWORKS_API_KEY=your_fireworks_api_key_here
DEEPINFRA_API_KEY=your_deepinfra_api_key_here

# Primary AI Provider Selection
AI_PROVIDER=groq

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================

# Target Performance
TARGET_ACCURACY=87
MIN_SIGNAL_CONFIDENCE=80
MIN_SHARPE_RATIO=2.0
MAX_DRAWDOWN=15

# Signal Generation
CURRENCY_PAIR=EUR/USD
MAX_DAILY_SIGNALS=12
SIGNAL_GENERATION_INTERVAL=2
MIN_CANDLES_FOR_ANALYSIS=500

# =============================================================================
# MULTI-SOURCE DATA FUSION SETTINGS
# =============================================================================

ENABLE_DATA_FUSION=true
MIN_DATA_SOURCES=2
CROSS_VERIFY_CANDLES=true
FILL_MISSING_DATA=true
AUTO_FALLBACK=true

# =============================================================================
# THREE-BRAIN CONSENSUS SETTINGS
# =============================================================================

# Quant Brain (ML Models)
USE_ENSEMBLE=true
ENSEMBLE_SIZE=5
REQUIRE_QUANT_CONFIDENCE=80

# Analyst Brain (AI Validation)
REQUIRE_ANALYST_VALIDATION=true
MIN_CONFLUENCE_SCORE=75
ENABLE_CONFLUENCE_FILTER=true

# Reflex Brain (Real-time Decision)
REQUIRE_REFLEX_APPROVAL=true
MIN_BRAIN_AGREEMENT=3

# =============================================================================
# TECHNICAL ANALYSIS SETTINGS
# =============================================================================

# Multi-Timeframe Analysis
TIMEFRAMES=1m,3m,5m,15m,30m,1h,4h
HISTORICAL_LOOKBACK=1000
ENABLE_MULTI_TIMEFRAME=true

# Pattern Recognition
ENABLE_PATTERN_VALIDATION=true
REQUIRE_VOLUME_CONFIRMATION=true

# Adaptive Indicators
ENABLE_ADAPTIVE_INDICATORS=true
OPTIMIZATION_WINDOW=50
REOPTIMIZATION_INTERVAL=86400000

# =============================================================================
# RISK MANAGEMENT FILTERS
# =============================================================================

# Volatility Filters
AVOID_HIGH_VOLATILITY=true
MAX_VOLATILITY_THRESHOLD=2.0

# Volume Filters
AVOID_LOW_VOLUME=true
MIN_VOLUME_RATIO=0.8

# Time-based Filters
AVOID_NEWS_EVENTS=true
NEWS_BUFFER_MINUTES=30
AVOID_MARKET_OPEN_CLOSE=true
MARKET_BUFFER_MINUTES=15

# Technical Filters
REJECT_CONFLICTING_SIGNALS=true
REJECT_UNCERTAINTY_CANDLES=true
REJECT_SUDDEN_SPIKES=true
MAX_WICK_RATIO=0.7

# Safe Zones
SAFE_ZONES_ONLY=false
AVOID_SUPPORT_RESISTANCE=true
SR_BUFFER_PIPS=5

# =============================================================================
# LEARNING & BACKTESTING
# =============================================================================

# AI Learning System
ENABLE_AI_LEARNING=true
RETRAIN_INTERVAL=86400000
MIN_SAMPLES_FOR_RETRAIN=100

# Backtesting
ENABLE_BACKTESTING=true
BACKTEST_PERIOD_MONTHS=6

# =============================================================================
# SYSTEM PERFORMANCE
# =============================================================================

# Processing Limits
MAX_PROCESSING_TIME=15000
SIGNAL_TIMEOUT=10000

# Data Quality
MIN_DATA_QUALITY_SCORE=70
ENABLE_PRE_SIGNAL_VALIDATION=true

# =============================================================================
# LOGGING & MONITORING
# =============================================================================

# Log Levels: error, warn, info, debug
LOG_LEVEL=info
LOG_TO_FILE=true
LOG_DIRECTORY=./logs

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
MONITORING_INTERVAL=300000

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

NODE_ENV=production
DEBUG_MODE=false
ENABLE_DETAILED_LOGGING=false

# =============================================================================
# ADVANCED FEATURES
# =============================================================================

# Market Regime Detection
ENABLE_REGIME_DETECTION=true
REGIME_LOOKBACK_PERIODS=100

# Dynamic Risk Adjustment
ENABLE_DYNAMIC_RISK=true
RISK_ADJUSTMENT_FACTOR=1.0

# Signal Quality Scoring
ENABLE_QUALITY_SCORING=true
MIN_QUALITY_SCORE=75

# Real-time Context Analysis
ENABLE_CONTEXT_ANALYSIS=true
CONTEXT_WEIGHT=0.25

# =============================================================================
# NOTIFICATION SETTINGS
# =============================================================================

ENABLE_NOTIFICATIONS=true
NOTIFICATION_WEBHOOK_URL=
ENABLE_EMAIL_ALERTS=false
EMAIL_SMTP_HOST=
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=
EMAIL_PASSWORD=
EMAIL_TO=

# =============================================================================
# WEB INTERFACE SETTINGS
# =============================================================================

WEB_PORT=3000
ENABLE_WEB_INTERFACE=true
ENABLE_API_ENDPOINTS=true
API_RATE_LIMIT=100

# =============================================================================
# DATABASE SETTINGS (Optional)
# =============================================================================

DATABASE_URL=
ENABLE_DATABASE_LOGGING=false
DATABASE_TYPE=sqlite

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

API_SECRET_KEY=your_secret_key_here
ENABLE_API_AUTHENTICATION=false
CORS_ORIGINS=http://localhost:3000

# =============================================================================
# EXPERIMENTAL FEATURES
# =============================================================================

ENABLE_EXPERIMENTAL_FEATURES=false
EXPERIMENTAL_MODEL_ENSEMBLE=false
EXPERIMENTAL_DEEP_LEARNING=false