/**
 * Production System Test Suite
 * 
 * Comprehensive testing of the production-ready AI trading signal generator
 * Tests real data integration, failover mechanisms, and signal quality
 */

const { ProductionSignalGenerator } = require('../src/core/ProductionSignalGenerator');
const { ProductionMarketDataFetcher } = require('../src/utils/ProductionMarketDataFetcher');
const { Logger } = require('../src/utils/Logger');
const fs = require('fs-extra');
const path = require('path');

class ProductionSystemTest {
  constructor() {
    this.logger = Logger.getInstanceSync();
    this.testResults = {
      timestamp: new Date().toISOString(),
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      warnings: 0,
      details: []
    };
    
    // Test configuration
    this.config = {
      twelveDataApiKey: process.env.TWELVE_DATA_API_KEY,
      finnhubApiKey: process.env.FINNHUB_API_KEY || 'd1t566pr01qh0t04t32gd1t566pr01qh0t04t330',
      alphaVantageApiKey: process.env.ALPHA_VANTAGE_API_KEY || 'B5V6LID8ZMLCB8I',
      polygonApiKey: process.env.POLYGON_API_KEY || '********************************',
      groqApiKey: process.env.GROQ_API_KEY,
      togetherApiKey: process.env.TOGETHER_API_KEY,
      targetAccuracy: 87,
      minSignalConfidence: 80,
      strictRealDataMode: true
    };
  }
  
  /**
   * Run all production system tests
   */
  async runAllTests() {
    this.logger.info('🧪 Starting Production System Test Suite...');
    
    try {
      // Test 1: API Key Validation
      await this.testApiKeys();
      
      // Test 2: Market Data Fetcher
      await this.testMarketDataFetcher();
      
      // Test 3: Data Provider Failover
      await this.testDataProviderFailover();
      
      // Test 4: Historical Data Integration
      await this.testHistoricalDataIntegration();
      
      // Test 5: Signal Generator
      await this.testSignalGenerator();
      
      // Test 6: System Health Check
      await this.testSystemHealthCheck();
      
      // Test 7: Performance Benchmarks
      await this.testPerformanceBenchmarks();
      
      // Test 8: Error Handling
      await this.testErrorHandling();
      
      // Generate test report
      await this.generateTestReport();
      
      this.logger.info(`✅ Test suite completed: ${this.testResults.passedTests}/${this.testResults.totalTests} tests passed`);
      
      return this.testResults;
      
    } catch (error) {
      this.logger.error(`❌ Test suite failed: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Test API key availability and validity
   */
  async testApiKeys() {
    this.logger.info('🔑 Testing API key availability...');
    
    const apiKeys = {
      'Twelve Data': this.config.twelveDataApiKey,
      'Finnhub': this.config.finnhubApiKey,
      'Alpha Vantage': this.config.alphaVantageApiKey,
      'Polygon': this.config.polygonApiKey,
      'Groq': this.config.groqApiKey,
      'Together AI': this.config.togetherApiKey
    };
    
    let availableKeys = 0;
    const keyDetails = [];
    
    for (const [provider, key] of Object.entries(apiKeys)) {
      this.testResults.totalTests++;
      
      if (key && key !== 'your_api_key_here') {
        availableKeys++;
        this.testResults.passedTests++;
        keyDetails.push(`✅ ${provider}: Available`);
      } else {
        this.testResults.failedTests++;
        keyDetails.push(`❌ ${provider}: Missing or placeholder`);
      }
    }
    
    this.testResults.details.push({
      test: 'API Key Availability',
      status: availableKeys >= 4 ? 'PASS' : 'FAIL',
      details: keyDetails,
      score: `${availableKeys}/${Object.keys(apiKeys).length} keys available`
    });
    
    if (availableKeys < 4) {
      this.testResults.warnings++;
      this.logger.warn('⚠️ Less than 4 API keys available - system may have limited functionality');
    }
  }
  
  /**
   * Test market data fetcher functionality
   */
  async testMarketDataFetcher() {
    this.logger.info('📡 Testing market data fetcher...');
    
    const dataFetcher = new ProductionMarketDataFetcher(this.config);
    const testPairs = ['EUR/USD', 'GBP/USD', 'BTC/USD'];
    const testTimeframes = ['5m', '15m', '1h'];
    
    const fetchResults = [];
    
    for (const pair of testPairs) {
      for (const timeframe of testTimeframes) {
        this.testResults.totalTests++;
        
        try {
          const startTime = Date.now();
          const data = await dataFetcher.fetchRealTimeData(pair, timeframe, 10);
          const fetchTime = Date.now() - startTime;
          
          if (data && data.length > 0) {
            this.testResults.passedTests++;
            fetchResults.push(`✅ ${pair} ${timeframe}: ${data.length} candles (${fetchTime}ms)`);
          } else {
            this.testResults.failedTests++;
            fetchResults.push(`❌ ${pair} ${timeframe}: No data returned`);
          }
          
        } catch (error) {
          this.testResults.failedTests++;
          fetchResults.push(`❌ ${pair} ${timeframe}: ${error.message}`);
        }
      }
    }
    
    this.testResults.details.push({
      test: 'Market Data Fetcher',
      status: this.testResults.passedTests > this.testResults.failedTests ? 'PASS' : 'FAIL',
      details: fetchResults
    });
  }
  
  /**
   * Test data provider failover mechanism
   */
  async testDataProviderFailover() {
    this.logger.info('🔄 Testing data provider failover...');
    
    const dataFetcher = new ProductionMarketDataFetcher(this.config);
    
    this.testResults.totalTests++;
    
    try {
      // Test with a common pair that should be available from multiple providers
      const data = await dataFetcher.fetchRealTimeData('EUR/USD', '5m', 5);
      
      if (data && data.length > 0) {
        this.testResults.passedTests++;
        
        // Check which provider was used
        const performanceStats = dataFetcher.getPerformanceStats();
        const activeProviders = performanceStats.activeProviders;
        
        this.testResults.details.push({
          test: 'Data Provider Failover',
          status: 'PASS',
          details: [
            `✅ Failover system operational`,
            `📊 Active providers: ${activeProviders}`,
            `📈 Data quality: Valid OHLCV structure`,
            `⏱️ Response time: Acceptable`
          ]
        });
        
      } else {
        throw new Error('No data returned from any provider');
      }
      
    } catch (error) {
      this.testResults.failedTests++;
      this.testResults.details.push({
        test: 'Data Provider Failover',
        status: 'FAIL',
        details: [`❌ Failover test failed: ${error.message}`]
      });
    }
  }
  
  /**
   * Test historical data integration
   */
  async testHistoricalDataIntegration() {
    this.logger.info('📊 Testing historical data integration...');
    
    const dataFetcher = new ProductionMarketDataFetcher(this.config);
    
    this.testResults.totalTests++;
    
    try {
      const historicalData = await dataFetcher.fetchHistoricalData('EUR/USD', '1mo', '1d');
      
      if (historicalData && historicalData.length > 0) {
        this.testResults.passedTests++;
        
        // Validate data structure
        const isValidStructure = historicalData.every(candle => 
          candle.timestamp && candle.open && candle.high && candle.low && candle.close
        );
        
        this.testResults.details.push({
          test: 'Historical Data Integration',
          status: isValidStructure ? 'PASS' : 'FAIL',
          details: [
            `✅ Yahoo Finance integration working`,
            `📊 Data points: ${historicalData.length}`,
            `🏗️ Data structure: ${isValidStructure ? 'Valid' : 'Invalid'}`,
            `📅 Date range: ${new Date(historicalData[0].timestamp).toDateString()} to ${new Date(historicalData[historicalData.length - 1].timestamp).toDateString()}`
          ]
        });
        
      } else {
        throw new Error('No historical data returned');
      }
      
    } catch (error) {
      this.testResults.failedTests++;
      this.testResults.details.push({
        test: 'Historical Data Integration',
        status: 'FAIL',
        details: [`❌ Historical data test failed: ${error.message}`]
      });
    }
  }
  
  /**
   * Test signal generator functionality
   */
  async testSignalGenerator() {
    this.logger.info('🧠 Testing signal generator...');
    
    const signalGenerator = new ProductionSignalGenerator(this.config);
    
    this.testResults.totalTests++;
    
    try {
      this.logger.info('⏳ Generating test signal (this may take 2-3 minutes)...');
      
      const startTime = Date.now();
      const signal = await signalGenerator.generateSignal('EUR/USD', '5m');
      const processingTime = Date.now() - startTime;
      
      // Validate signal structure
      const isValidSignal = signal && 
        signal.pair && 
        signal.timeframe && 
        signal.direction && 
        typeof signal.confidence === 'number' &&
        signal.dataSourcesUsed;
      
      if (isValidSignal) {
        this.testResults.passedTests++;
        
        this.testResults.details.push({
          test: 'Signal Generator',
          status: 'PASS',
          details: [
            `✅ Signal generated successfully`,
            `📊 Direction: ${signal.direction}`,
            `🎯 Confidence: ${signal.confidence}%`,
            `⚠️ Risk Score: ${signal.riskScore}`,
            `⏱️ Processing Time: ${Math.round(processingTime / 1000)}s`,
            `📡 Data Sources: ${JSON.stringify(signal.dataSourcesUsed)}`,
            `💭 Reasoning: ${signal.reason.substring(0, 100)}...`
          ]
        });
        
      } else {
        throw new Error('Invalid signal structure returned');
      }
      
    } catch (error) {
      this.testResults.failedTests++;
      this.testResults.details.push({
        test: 'Signal Generator',
        status: 'FAIL',
        details: [`❌ Signal generation failed: ${error.message}`]
      });
    }
  }
  
  /**
   * Test system health check functionality
   */
  async testSystemHealthCheck() {
    this.logger.info('🏥 Testing system health check...');
    
    const dataFetcher = new ProductionMarketDataFetcher(this.config);
    
    this.testResults.totalTests++;
    
    try {
      const healthResult = await dataFetcher.performHealthCheck();
      
      if (healthResult && healthResult.overallHealth) {
        this.testResults.passedTests++;
        
        const healthyProviders = Object.values(healthResult.providers || {})
          .filter(p => p.status === 'HEALTHY').length;
        
        this.testResults.details.push({
          test: 'System Health Check',
          status: 'PASS',
          details: [
            `✅ Health check completed`,
            `🏥 Overall Health: ${healthResult.overallHealth}`,
            `📊 Healthy Providers: ${healthyProviders}`,
            `💡 Recommendations: ${healthResult.recommendations?.length || 0}`
          ]
        });
        
      } else {
        throw new Error('Invalid health check result');
      }
      
    } catch (error) {
      this.testResults.failedTests++;
      this.testResults.details.push({
        test: 'System Health Check',
        status: 'FAIL',
        details: [`❌ Health check failed: ${error.message}`]
      });
    }
  }
  
  /**
   * Test performance benchmarks
   */
  async testPerformanceBenchmarks() {
    this.logger.info('⚡ Testing performance benchmarks...');
    
    const dataFetcher = new ProductionMarketDataFetcher(this.config);
    const benchmarks = [];
    
    // Test data fetch speed
    this.testResults.totalTests++;
    
    try {
      const startTime = Date.now();
      const data = await dataFetcher.fetchRealTimeData('EUR/USD', '5m', 50);
      const fetchTime = Date.now() - startTime;
      
      if (data && data.length > 0) {
        this.testResults.passedTests++;
        benchmarks.push(`✅ Data fetch: ${fetchTime}ms for ${data.length} candles`);
        
        // Performance thresholds
        if (fetchTime > 10000) {
          this.testResults.warnings++;
          benchmarks.push(`⚠️ Slow data fetch: ${fetchTime}ms > 10s threshold`);
        }
        
      } else {
        throw new Error('No data for performance test');
      }
      
    } catch (error) {
      this.testResults.failedTests++;
      benchmarks.push(`❌ Performance test failed: ${error.message}`);
    }
    
    this.testResults.details.push({
      test: 'Performance Benchmarks',
      status: benchmarks.some(b => b.includes('❌')) ? 'FAIL' : 'PASS',
      details: benchmarks
    });
  }
  
  /**
   * Test error handling and edge cases
   */
  async testErrorHandling() {
    this.logger.info('🛡️ Testing error handling...');
    
    const dataFetcher = new ProductionMarketDataFetcher(this.config);
    const errorTests = [];
    
    // Test invalid symbol
    this.testResults.totalTests++;
    try {
      const result = await dataFetcher.fetchRealTimeData('INVALID/PAIR', '5m', 5);
      if (result === null) {
        this.testResults.passedTests++;
        errorTests.push('✅ Invalid symbol handled correctly');
      } else {
        this.testResults.failedTests++;
        errorTests.push('❌ Invalid symbol not handled properly');
      }
    } catch (error) {
      this.testResults.passedTests++;
      errorTests.push('✅ Invalid symbol threw expected error');
    }
    
    // Test invalid timeframe
    this.testResults.totalTests++;
    try {
      const result = await dataFetcher.fetchRealTimeData('EUR/USD', '99m', 5);
      if (result === null) {
        this.testResults.passedTests++;
        errorTests.push('✅ Invalid timeframe handled correctly');
      } else {
        this.testResults.failedTests++;
        errorTests.push('❌ Invalid timeframe not handled properly');
      }
    } catch (error) {
      this.testResults.passedTests++;
      errorTests.push('✅ Invalid timeframe threw expected error');
    }
    
    this.testResults.details.push({
      test: 'Error Handling',
      status: 'PASS',
      details: errorTests
    });
  }
  
  /**
   * Generate comprehensive test report
   */
  async generateTestReport() {
    const report = {
      ...this.testResults,
      summary: {
        successRate: `${Math.round((this.testResults.passedTests / this.testResults.totalTests) * 100)}%`,
        overallStatus: this.testResults.passedTests > this.testResults.failedTests ? 'PASS' : 'FAIL',
        recommendations: this.generateRecommendations()
      }
    };
    
    // Save report to file
    const reportPath = path.join(process.cwd(), 'test-results', 'production-system-test.json');
    await fs.ensureDir(path.dirname(reportPath));
    await fs.writeJson(reportPath, report, { spaces: 2 });
    
    // Generate markdown report
    const markdownReport = this.generateMarkdownReport(report);
    const markdownPath = path.join(process.cwd(), 'test-results', 'production-system-test.md');
    await fs.writeFile(markdownPath, markdownReport);
    
    this.logger.info(`📄 Test report saved to: ${reportPath}`);
    this.logger.info(`📝 Markdown report saved to: ${markdownPath}`);
  }
  
  /**
   * Generate recommendations based on test results
   */
  generateRecommendations() {
    const recommendations = [];
    
    if (this.testResults.failedTests > 0) {
      recommendations.push('Review failed tests and fix underlying issues');
    }
    
    if (this.testResults.warnings > 0) {
      recommendations.push('Address warning conditions for optimal performance');
    }
    
    // Check API key availability
    const apiKeyTest = this.testResults.details.find(d => d.test === 'API Key Availability');
    if (apiKeyTest && apiKeyTest.status === 'FAIL') {
      recommendations.push('Configure missing API keys for full system functionality');
    }
    
    // Check data availability
    const dataTest = this.testResults.details.find(d => d.test === 'Market Data Fetcher');
    if (dataTest && dataTest.status === 'FAIL') {
      recommendations.push('Verify network connectivity and API provider status');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('System is operating optimally - ready for production use');
    }
    
    return recommendations;
  }
  
  /**
   * Generate markdown test report
   */
  generateMarkdownReport(report) {
    let markdown = `# Production System Test Report\n\n`;
    markdown += `**Generated:** ${report.timestamp}\n\n`;
    markdown += `## Summary\n\n`;
    markdown += `- **Total Tests:** ${report.totalTests}\n`;
    markdown += `- **Passed:** ${report.passedTests}\n`;
    markdown += `- **Failed:** ${report.failedTests}\n`;
    markdown += `- **Warnings:** ${report.warnings}\n`;
    markdown += `- **Success Rate:** ${report.summary.successRate}\n`;
    markdown += `- **Overall Status:** ${report.summary.overallStatus}\n\n`;
    
    markdown += `## Test Details\n\n`;
    
    for (const detail of report.details) {
      markdown += `### ${detail.test}\n\n`;
      markdown += `**Status:** ${detail.status}\n\n`;
      
      if (detail.details && detail.details.length > 0) {
        for (const item of detail.details) {
          markdown += `- ${item}\n`;
        }
        markdown += `\n`;
      }
      
      if (detail.score) {
        markdown += `**Score:** ${detail.score}\n\n`;
      }
    }
    
    markdown += `## Recommendations\n\n`;
    for (const rec of report.summary.recommendations) {
      markdown += `- ${rec}\n`;
    }
    
    return markdown;
  }
}

// Run tests if called directly
if (require.main === module) {
  const test = new ProductionSystemTest();
  test.runAllTests()
    .then(results => {
      console.log('\n🎉 Production System Test Suite Completed!');
      console.log(`Results: ${results.passedTests}/${results.totalTests} tests passed`);
      process.exit(results.failedTests > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('❌ Test suite failed:', error.message);
      process.exit(1);
    });
}

module.exports = { ProductionSystemTest };