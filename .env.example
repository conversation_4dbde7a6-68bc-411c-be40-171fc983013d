# Production AI Trading Signal Generator Configuration
# Copy this file to .env and fill in your actual values

# ===========================================
# MARKET DATA API KEYS (MULTI-PROVIDER SETUP)
# ===========================================

# Primary Real-Time Data Provider
# Twelve Data API Key (for real-time forex/crypto data)
# Get your free API key at: https://twelvedata.com/
TWELVE_DATA_API_KEY=your_twelve_data_api_key_here

# Backup Real-Time Data Providers (Failover Chain)
# Finnhub API Key - Get at: https://finnhub.io/
FINNHUB_API_KEY=d1t566pr01qh0t04t32gd1t566pr01qh0t04t330

# Alpha Vantage API Key - Get at: https://www.alphavantage.co/
ALPHA_VANTAGE_API_KEY=B5V6LID8ZMLCB8I

# Polygon.io API Key - Get at: https://polygon.io/
POLYGON_API_KEY=********************************

# ===========================================
# AI PROVIDER API KEYS (3-BRAIN ARCHITECTURE)
# ===========================================

# Groq API Key (for Reflex Brain - fast inference)
# Get at: https://console.groq.com/
GROQ_API_KEY=your_groq_api_key_here

# Together AI API Key (for Analyst Brain - reasoning)
# Get at: https://api.together.xyz/
TOGETHER_API_KEY=your_together_api_key_here

# OpenAI API Key (optional - for enhanced analysis)
# Get at: https://platform.openai.com/
OPENAI_API_KEY=your_openai_api_key_here

# ===========================================
# QXBROKER CREDENTIALS (REQUIRED FOR LIVE TRADING)
# ===========================================

# Your QXBroker login credentials
QXBROKER_EMAIL=<EMAIL>
QXBROKER_PASSWORD=your_password_here

# ===========================================
# PRODUCTION SYSTEM CONFIGURATION
# ===========================================

# System Mode (PRODUCTION/DEVELOPMENT)
NODE_ENV=production

# Strict Real Data Mode (never use mock data)
STRICT_REAL_DATA_MODE=true

# Log data sources used for transparency
LOG_DATA_SOURCE=true

# Target accuracy percentage (85-90% range)
TARGET_ACCURACY=87

# Minimum signal confidence threshold (0-100)
MIN_SIGNAL_CONFIDENCE=80

# Maximum daily signals to prevent overtrading
MAX_DAILY_SIGNALS=12

# Enable AI learning and adaptation
ENABLE_AI_LEARNING=true

# Require consensus from all 3 brains
REQUIRE_CONSENSUS=true

# ===========================================
# TRADING CONFIGURATION
# ===========================================

# Default currency pair for testing
DEFAULT_CURRENCY_PAIR=EUR/USD

# Default timeframe for analysis
DEFAULT_TIMEFRAME=5m

# Paper trading mode (true/false, default: true for safety)
PAPER_TRADING=true

# Trade amount in USD (for paper trading)
TRADE_AMOUNT=10

# ===========================================
# FOREX SIGNAL GENERATOR CONFIGURATION
# ===========================================

# Sniper Mode Settings
SNIPER_MODE_SL_MIN_PIPS=3
SNIPER_MODE_SL_MAX_PIPS=5
SNIPER_MODE_TP_MIN_PIPS=6
SNIPER_MODE_TP_MAX_PIPS=8
SNIPER_MODE_MIN_CONFIDENCE=70

# Scalping Mode Settings
SCALPING_MODE_SL_MIN_PIPS=8
SCALPING_MODE_SL_MAX_PIPS=12
SCALPING_MODE_TP_MIN_PIPS=15
SCALPING_MODE_TP_MAX_PIPS=25
SCALPING_MODE_MIN_CONFIDENCE=80

# Swing Mode Settings
SWING_MODE_SL_MIN_PIPS=20
SWING_MODE_SL_MAX_PIPS=30
SWING_MODE_TP_MIN_PIPS=50
SWING_MODE_TP_MAX_PIPS=100
SWING_MODE_MIN_CONFIDENCE=85

# Default risk percentage per trade
DEFAULT_RISK_PERCENTAGE=1

# ===========================================
# SELENIUM CONFIGURATION
# ===========================================

# Run browser in headless mode (true/false, default: true)
SELENIUM_HEADLESS=true

# ===========================================
# LOGGING CONFIGURATION
# ===========================================

# Log level (debug, info, warn, error, default: info)
LOG_LEVEL=info

# ===========================================
# ADVANCED SETTINGS (OPTIONAL)
# ===========================================

# Maximum trades per day (default: 50)
MAX_DAILY_TRADES=50

# Maximum consecutive losses before stopping (default: 3)
MAX_CONSECUTIVE_LOSSES=3
