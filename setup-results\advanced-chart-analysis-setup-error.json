{"timestamp": "2025-07-23T12:34:12.299Z", "error": "All installation methods failed: requiredDependencies is not defined", "setupSteps": [{"name": "Prerequisites Check", "startTime": 1753273724407, "success": true, "duration": 23}], "errors": ["Command failed: npm install opencv4nodejs @tensorflow/tfjs-node canvas image-size file-type jimp formidable\nnpm warn deprecated npmlog@4.1.2: This package is no longer supported.\nnpm warn deprecated npmlog@4.1.2: This package is no longer supported.\nnpm warn deprecated rimraf@2.7.1: Rimraf versions prior to v4 are no longer supported\nnpm warn deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported\nnpm warn deprecated are-we-there-yet@1.1.7: This package is no longer supported.\nnpm warn deprecated are-we-there-yet@1.1.7: This package is no longer supported.\nnpm warn deprecated gauge@2.7.4: This package is no longer supported.\nnpm warn deprecated gauge@2.7.4: This package is no longer supported.\nnpm warn cleanup Failed to remove some directories [\nnpm warn cleanup   [\nnpm warn cleanup     '\\\\\\\\?\\\\E:\\\\Ranveer\\\\TRADAI\\\\node_modules\\\\canvas',\nnpm warn cleanup     [Error: EBUSY: resource busy or locked, rmdir 'E:\\Ranveer\\TRADAI\\node_modules\\canvas'] {\nnpm warn cleanup       errno: -4082,\nnpm warn cleanup       code: 'EBUSY',\nnpm warn cleanup       syscall: 'rmdir',\nnpm warn cleanup       path: 'E:\\\\Ranveer\\\\TRADAI\\\\node_modules\\\\canvas'\nnpm warn cleanup     }\nnpm warn cleanup   ],\nnpm warn cleanup   [\nnpm warn cleanup     '\\\\\\\\?\\\\E:\\\\Ranveer\\\\TRADAI\\\\node_modules\\\\@tensorflow\\\\tfjs-node',\nnpm warn cleanup     [Error: EBUSY: resource busy or locked, rmdir 'E:\\Ranveer\\TRADAI\\node_modules\\@tensorflow\\tfjs-node'] {\nnpm warn cleanup       errno: -4082,\nnpm warn cleanup       code: 'EBUSY',\nnpm warn cleanup       syscall: 'rmdir',\nnpm warn cleanup       path: 'E:\\\\Ranveer\\\\TRADAI\\\\node_modules\\\\@tensorflow\\\\tfjs-node'\nnpm warn cleanup     }\nnpm warn cleanup   ],\nnpm warn cleanup   [\nnpm warn cleanup     '\\\\\\\\?\\\\E:\\\\Ranveer\\\\TRADAI\\\\node_modules\\\\opencv-build',\nnpm warn cleanup     [Error: EBUSY: resource busy or locked, rmdir 'E:\\Ranveer\\TRADAI\\node_modules\\opencv-build'] {\nnpm warn cleanup       errno: -4082,\nnpm warn cleanup       code: 'EBUSY',\nnpm warn cleanup       syscall: 'rmdir',\nnpm warn cleanup       path: 'E:\\\\Ranveer\\\\TRADAI\\\\node_modules\\\\opencv-build'\nnpm warn cleanup     }\nnpm warn cleanup   ]\nnpm warn cleanup ]\nnpm error code 1\nnpm error path E:\\Ranveer\\TRADAI\\node_modules\\opencv4nodejs\nnpm error command failed\nnpm error command C:\\WINDOWS\\system32\\cmd.exe /d /s /c node ./install/install.js\nnpm error info install using lib dir: E:/Ranveer/TRADAI/node_modules/opencv-build/opencv/build/lib/Release\nnpm error E:\\Ranveer\\TRADAI\\node_modules\\opencv4nodejs\\install\\install.js:37\nnpm error   throw new Error('library dir does not exist: ' + libDir)\nnpm error   ^\nnpm error\nnpm error Error: library dir does not exist: E:/Ranveer/TRADAI/node_modules/opencv-build/opencv/build/lib/Release\nnpm error     at Object.<anonymous> (E:\\Ranveer\\TRADAI\\node_modules\\opencv4nodejs\\install\\install.js:37:9)\nnpm error     at Module._compile (node:internal/modules/cjs/loader:1730:14)\nnpm error     at Object..js (node:internal/modules/cjs/loader:1895:10)\nnpm error     at Module.load (node:internal/modules/cjs/loader:1465:32)\nnpm error     at Function._load (node:internal/modules/cjs/loader:1282:12)\nnpm error     at TracingChannel.traceSync (node:diagnostics_channel:322:14)\nnpm error     at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\nnpm error     at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)\nnpm error     at node:internal/main/run_main_module:36:49\nnpm error\nnpm error Node.js v22.15.0\nnpm error A complete log of this run can be found in: C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-07-23T12_28_46_377Z-debug-0.log\n"], "warnings": ["Low available memory detected. Performance may be affected."], "systemInfo": {"nodeVersion": "v22.15.0", "platform": "win32", "architecture": "x64"}}