{"timestamp": "2025-07-23T06:04:19.832Z", "imageTests": [{"endpoint": "/api/otc-signal-generator", "testCase": {"currencyPair": "USD/PKR", "timeframe": "1m", "tradeDuration": "1", "platform": "quotex", "description": "USD/PKR 1-minute chart analysis"}, "status": 422, "error": "Request failed with status code 422", "errorData": {"success": false, "requestId": "API_1753250659882_MTc1MzI1", "processingTime": 1, "error": "STRICT_MODE_VIOLATION", "message": "STRICT_MODE_VIOLATION: Signal missing data source metadata", "strictMode": true, "currency_pair": "USD/PKR", "timeframe": "1m", "trade_duration": "1", "timestamp": "2025-07-23T06:04:19.883Z"}, "success": false, "timestamp": "2025-07-23T06:04:20.639Z"}, {"endpoint": "/api/otc-signal-generator", "testCase": {"currencyPair": "USD/PKR", "timeframe": "3m", "tradeDuration": "3", "platform": "quotex", "description": "USD/PKR 3-minute chart analysis"}, "status": 422, "error": "Request failed with status code 422", "errorData": {"success": false, "requestId": "API_1753250670505_MTc1MzI1", "processingTime": 2, "error": "STRICT_MODE_VIOLATION", "message": "STRICT_MODE_VIOLATION: Signal missing data source metadata", "strictMode": true, "currency_pair": "USD/PKR", "timeframe": "3m", "trade_duration": "3", "timestamp": "2025-07-23T06:04:30.507Z"}, "success": false, "timestamp": "2025-07-23T06:04:31.106Z"}, {"endpoint": "/api/otc-signal-generator", "testCase": {"currencyPair": "USD/PKR", "timeframe": "5m", "tradeDuration": "5", "platform": "quotex", "description": "USD/PKR 5-minute chart analysis"}, "status": 422, "error": "Request failed with status code 422", "errorData": {"success": false, "requestId": "API_1753250680970_MTc1MzI1", "processingTime": 1, "error": "STRICT_MODE_VIOLATION", "message": "STRICT_MODE_VIOLATION: Signal missing data source metadata", "strictMode": true, "currency_pair": "USD/PKR", "timeframe": "5m", "trade_duration": "5", "timestamp": "2025-07-23T06:04:40.971Z"}, "success": false, "timestamp": "2025-07-23T06:04:41.613Z"}, {"endpoint": "/api/otc-signal-generator", "testCase": {"currencyPair": "USD/DZD", "timeframe": "1m", "tradeDuration": "1", "platform": "quotex", "description": "USD/DZD 1-minute chart analysis"}, "status": 422, "error": "Request failed with status code 422", "errorData": {"success": false, "requestId": "API_1753250691459_MTc1MzI1", "processingTime": 2, "error": "STRICT_MODE_VIOLATION", "message": "STRICT_MODE_VIOLATION: Signal missing data source metadata", "strictMode": true, "currency_pair": "USD/DZD", "timeframe": "1m", "trade_duration": "1", "timestamp": "2025-07-23T06:04:51.461Z"}, "success": false, "timestamp": "2025-07-23T06:04:52.070Z"}, {"endpoint": "/api/otc-signal-generator", "testCase": {"currencyPair": "USD/DZD", "timeframe": "3m", "tradeDuration": "3", "platform": "quotex", "description": "USD/DZD 3-minute chart analysis"}, "status": 422, "error": "Request failed with status code 422", "errorData": {"success": false, "requestId": "API_1753250701949_MTc1MzI1", "processingTime": 1, "error": "STRICT_MODE_VIOLATION", "message": "STRICT_MODE_VIOLATION: Signal missing data source metadata", "strictMode": true, "currency_pair": "USD/DZD", "timeframe": "3m", "trade_duration": "3", "timestamp": "2025-07-23T06:05:01.950Z"}, "success": false, "timestamp": "2025-07-23T06:05:02.605Z"}, {"endpoint": "/api/otc-signal-generator", "testCase": {"currencyPair": "USD/DZD", "timeframe": "5m", "tradeDuration": "5", "platform": "quotex", "description": "USD/DZD 5-minute chart analysis"}, "status": 422, "error": "Request failed with status code 422", "errorData": {"success": false, "requestId": "API_1753250712448_MTc1MzI1", "processingTime": 1, "error": "STRICT_MODE_VIOLATION", "message": "STRICT_MODE_VIOLATION: Signal missing data source metadata", "strictMode": true, "currency_pair": "USD/DZD", "timeframe": "5m", "trade_duration": "5", "timestamp": "2025-07-23T06:05:12.449Z"}, "success": false, "timestamp": "2025-07-23T06:05:13.062Z"}, {"endpoint": "/api/qxbroker-otc-signal", "testCase": {"currencyPair": "USD/PKR", "timeframe": "1m", "tradeDuration": "1", "platform": "quotex", "description": "USD/PKR 1-minute chart analysis"}, "status": 500, "error": "Request failed with status code 500", "errorData": {"success": false, "error": "Signal generation failed", "message": "ENOENT: no such file or directory, mkdir '/var/task/data/temp'", "requestId": "API_1753250712739_8i44j3o5e", "processingTime": 0, "signal": "ERROR", "confidence": "0%", "riskScore": "HIGH"}, "success": false, "timestamp": "2025-07-23T06:05:13.356Z"}, {"endpoint": "/api/qxbroker-otc-signal", "testCase": {"currencyPair": "USD/PKR", "timeframe": "3m", "tradeDuration": "3", "platform": "quotex", "description": "USD/PKR 3-minute chart analysis"}, "status": 429, "error": "Request failed with status code 429", "errorData": {"error": "Rate limit exceeded", "message": "Request too soon. Wait 20 seconds", "requestId": "API_1753250723197_j1vyh4rto", "retryAfter": 1753250742739}, "success": false, "timestamp": "2025-07-23T06:05:23.906Z"}, {"endpoint": "/api/qxbroker-otc-signal", "testCase": {"currencyPair": "USD/PKR", "timeframe": "5m", "tradeDuration": "5", "platform": "quotex", "description": "USD/PKR 5-minute chart analysis"}, "status": 500, "error": "Request failed with status code 500", "errorData": {"success": false, "error": "Signal generation failed", "message": "ENOENT: no such file or directory, mkdir '/var/task/data/temp'", "requestId": "API_1753250763770_amtw6jv6n", "processingTime": 0, "signal": "ERROR", "confidence": "0%", "riskScore": "HIGH"}, "success": false, "timestamp": "2025-07-23T06:06:04.388Z"}, {"endpoint": "/api/qxbroker-otc-signal", "testCase": {"currencyPair": "USD/DZD", "timeframe": "1m", "tradeDuration": "1", "platform": "quotex", "description": "USD/DZD 1-minute chart analysis"}, "status": 429, "error": "Request failed with status code 429", "errorData": {"error": "Rate limit exceeded", "message": "Request too soon. Wait 20 seconds", "requestId": "API_1753250774281_sorkf95u5", "retryAfter": 1753250793770}, "success": false, "timestamp": "2025-07-23T06:06:15.001Z"}, {"endpoint": "/api/qxbroker-otc-signal", "testCase": {"currencyPair": "USD/DZD", "timeframe": "3m", "tradeDuration": "3", "platform": "quotex", "description": "USD/DZD 3-minute chart analysis"}, "status": 500, "error": "Request failed with status code 500", "errorData": {"success": false, "error": "Signal generation failed", "message": "ENOENT: no such file or directory, mkdir '/var/task/data/temp'", "requestId": "API_1753250814822_fpsn7ymuy", "processingTime": 0, "signal": "ERROR", "confidence": "0%", "riskScore": "HIGH"}, "success": false, "timestamp": "2025-07-23T06:06:55.451Z"}, {"endpoint": "/api/qxbroker-otc-signal", "testCase": {"currencyPair": "USD/DZD", "timeframe": "5m", "tradeDuration": "5", "platform": "quotex", "description": "USD/DZD 5-minute chart analysis"}, "status": 429, "error": "Request failed with status code 429", "errorData": {"error": "Rate limit exceeded", "message": "Request too soon. Wait 20 seconds", "requestId": "API_1753250825325_46acfeshy", "retryAfter": 1753250844822}, "success": false, "timestamp": "2025-07-23T06:07:06.001Z"}, {"endpoint": "/api/otc-signal-generator", "testCase": {"asset": "USD/PKR", "timeframe": "1m", "duration": "1", "imageType": "chart_screenshot", "description": "USD/PKR 1m chart (simulated)"}, "status": 400, "error": "Request failed with status code 400", "success": false, "timestamp": "2025-07-23T06:07:41.533Z", "testType": "image_simulation"}, {"endpoint": "/api/otc-signal-generator", "testCase": {"asset": "USD/DZD", "timeframe": "5m", "duration": "5", "imageType": "chart_screenshot", "description": "USD/DZD 5m chart (simulated)"}, "status": 400, "error": "Request failed with status code 400", "success": false, "timestamp": "2025-07-23T06:07:46.990Z", "testType": "image_simulation"}, {"endpoint": "/api/qxbroker-otc-signal", "testCase": {"asset": "USD/PKR", "timeframe": "1m", "duration": "1", "imageType": "chart_screenshot", "description": "USD/PKR 1m chart (simulated)"}, "status": 500, "error": "Request failed with status code 500", "success": false, "timestamp": "2025-07-23T06:07:53.100Z", "testType": "image_simulation"}, {"endpoint": "/api/qxbroker-otc-signal", "testCase": {"asset": "USD/DZD", "timeframe": "5m", "duration": "5", "imageType": "chart_screenshot", "description": "USD/DZD 5m chart (simulated)"}, "status": 429, "error": "Request failed with status code 429", "success": false, "timestamp": "2025-07-23T06:07:58.522Z", "testType": "image_simulation"}], "validation": {"strictMode": false, "realDataUsage": false, "qualityScores": [], "confidenceScores": []}}