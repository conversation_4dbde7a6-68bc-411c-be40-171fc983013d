# AI Binary Trading Bot Configuration
# Generated by setup wizard on 2025-07-03T13:35:45.917Z

# ===========================================
# API KEYS - MULTI-SOURCE DATA FUSION
# ===========================================

# Primary Market Data Providers
TWELVE_DATA_API_KEY=72ada78cc4484a7cad2acec5c5b8097c
FINNHUB_API_KEY=d1t566pr01qh0t04t32gd1t566pr01qh0t04t330
ALPHA_VANTAGE_API_KEY=B5V6LID8ZMLCB8I
POLYGON_API_KEY=********************************

# AI Provider API Keys
GROQ_API_KEY=********************************************************
TOGETHER_API_KEY=********************************************************
OPENROUTER_API_KEY=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
FIREWORKS_API_KEY=fw_3Zh7nBAhSbToeJWKyoSrEAQM
DEEPINFRA_API_KEY=54mrgC36UY7vlElQTl9kJl3aj86XeRQN

# ===========================================
# QXBROKER CREDENTIALS
# ===========================================
QXBROKER_EMAIL=<EMAIL>
QXBROKER_PASSWORD=India#321

# ===========================================
# TRADING CONFIGURATION
# ===========================================
CURRENCY_PAIR=USD/EUR
TRADE_AMOUNT=10
MIN_CONFIDENCE=70
PAPER_TRADING=false
SIGNAL_ONLY=true
AI_PROVIDER=groq
TRADE_EXECUTION=false
REQUIRE_CONSENSUS=true

# ===========================================
# SELENIUM CONFIGURATION
# ===========================================
SELENIUM_HEADLESS=true

# ===========================================
# ENHANCED TRADAI CONFIGURATION - 85-90% ACCURACY TARGET
# ===========================================

# WebSocket server port for Chrome extension connection
WEBSOCKET_PORT=8080

# Account balance for position sizing calculations
ACCOUNT_BALANCE=1000

# Trading timeframe (1min, 2min, 5min, 15min, 30min, 1h)
TIMEFRAME=5min

# Maximum trades per day (high-quality signals only)
MAX_DAILY_TRADES=12

# Maximum consecutive losses before stopping
MAX_CONSECUTIVE_LOSSES=3

# Database file path for trade history and AI learning
DATABASE_PATH=./data/tradai.db

# Enable AI learning from trade outcomes
ENABLE_AI_LEARNING=true

# Use Kelly Criterion for position sizing
USE_KELLY_CRITERION=true

# Maximum risk per trade as percentage of account
MAX_RISK_PER_TRADE=2.0

# Daily loss limit as percentage of account
DAILY_LOSS_LIMIT=10.0

# ===========================================
# MULTI-SOURCE DATA FUSION CONFIGURATION
# ===========================================

# Data collection interval in minutes
DATA_COLLECTION_INTERVAL=1

# Signal generation interval in minutes
SIGNAL_GENERATION_INTERVAL=2

# Historical data lookback period in candles
HISTORICAL_LOOKBACK=1000

# Minimum candles required for analysis
MIN_CANDLES_FOR_ANALYSIS=500

# Multi-timeframe analysis
ENABLE_MULTI_TIMEFRAME=true
TIMEFRAMES=1m,3m,5m,15m,30m,1h,4h

# Data fusion settings
ENABLE_DATA_FUSION=true
MIN_DATA_SOURCES=2
CROSS_VERIFY_CANDLES=true
FILL_MISSING_DATA=true
AUTO_FALLBACK=true

# ===========================================
# ACCURACY & PERFORMANCE TARGETS
# ===========================================

# Minimum signal confidence (80%+ only)
MIN_SIGNAL_CONFIDENCE=80

# Target accuracy percentage
TARGET_ACCURACY=87

# Minimum Sharpe ratio
MIN_SHARPE_RATIO=2.0

# Maximum drawdown percentage
MAX_DRAWDOWN=15

# Signal quality filters
ENABLE_CONFLUENCE_FILTER=true
MIN_CONFLUENCE_SCORE=75
REQUIRE_VOLUME_CONFIRMATION=true
ENABLE_PATTERN_VALIDATION=true

# Three-brain consensus requirements
REQUIRE_QUANT_CONFIDENCE=80
REQUIRE_ANALYST_VALIDATION=true
REQUIRE_REFLEX_APPROVAL=true
MIN_BRAIN_AGREEMENT=3

# ===========================================
# RISK MANAGEMENT & SIGNAL REJECTION
# ===========================================

# Market condition filters
AVOID_HIGH_VOLATILITY=true
MAX_VOLATILITY_THRESHOLD=2.0
AVOID_LOW_VOLUME=true
MIN_VOLUME_RATIO=0.8

# News and event filters
AVOID_NEWS_EVENTS=true
NEWS_BUFFER_MINUTES=30
AVOID_MARKET_OPEN_CLOSE=true
MARKET_BUFFER_MINUTES=15

# Technical filters
REJECT_CONFLICTING_SIGNALS=true
REJECT_UNCERTAINTY_CANDLES=true
REJECT_SUDDEN_SPIKES=true
MAX_WICK_RATIO=0.7

# Safe zones only
SAFE_ZONES_ONLY=true
AVOID_SUPPORT_RESISTANCE=true
SR_BUFFER_PIPS=5

# ===========================================
# LOGGING
# ===========================================
LOG_LEVEL=info
ENHANCED_LOGGING=true

# ===========================================
# REAL DATA ENFORCEMENT SETTINGS
# ===========================================

# Force real data mode (disable all mock data fallbacks)
STRICT_REAL_DATA_MODE=true

# Force real data in DataCollector
FORCE_REAL_DATA=true

# Log data source usage for monitoring
LOG_DATA_SOURCE=true

# Disable mock data usage
USE_MOCK_DATA=false

# Enable data source monitoring
MONITOR_DATA_SOURCES=true

# Alert on data source failures
ALERT_ON_DATA_FAILURE=true
