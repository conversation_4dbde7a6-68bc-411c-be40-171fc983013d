{"version": 2, "framework": "nextjs", "buildCommand": "npm run vercel-build", "devCommand": "npm run dev", "installCommand": "OPENCV4NODEJS_DISABLE_AUTOBUILD=1 npm install --no-optional", "env": {"NODE_ENV": "production", "VERCEL": "1", "OPENCV4NODEJS_DISABLE_AUTOBUILD": "1"}, "functions": {"pages/api/otc-signal-generator.js": {"maxDuration": 300}, "pages/api/otc-signal-generator/health.js": {"maxDuration": 30}, "pages/api/vercel-health.js": {"maxDuration": 10}, "pages/api/vercel-forex-signal.js": {"maxDuration": 10}, "pages/api/forex-signal-generator.ts": {"maxDuration": 30}}, "regions": ["iad1"], "routes": [{"src": "/api/otc-signal-generator/(.*)", "dest": "/api/otc-signal-generator/$1"}, {"src": "/otc-signal-generator", "dest": "/otc-signal-generator"}, {"src": "/api/health", "dest": "/api/vercel-health"}, {"src": "/api/forex-signal-generator", "dest": "/api/vercel-forex-signal"}, {"src": "/forex-signal-generator", "dest": "/forex-signal-generator"}]}