# AI Trading Sniper Backend Requirements

# FastAPI and server
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
pydantic==2.4.2

# Data processing and ML
numpy==1.24.3
pandas==2.1.1
scikit-learn==1.3.0
joblib==1.3.2

# Additional ML libraries (optional)
xgboost==1.7.6
lightgbm==4.1.0
catboost==1.2

# Technical analysis
ta-lib==0.4.25
talib-binary==0.4.24

# Database (optional)
sqlalchemy==2.0.23
sqlite3  # Built-in with Python

# Logging and monitoring
loguru==0.7.2
prometheus-client==0.17.1

# HTTP client for external APIs
httpx==0.25.0
aiohttp==3.8.6

# Configuration management
python-dotenv==1.0.0
pyyaml==6.0.1

# Testing
pytest==7.4.2
pytest-asyncio==0.21.1
httpx==0.25.0  # For testing FastAPI

# Development tools
black==23.9.1
flake8==6.1.0
mypy==1.6.1