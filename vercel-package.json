{"name": "tradai-signal-generator", "version": "1.0.0", "description": "AI-powered trading signal generator with React frontend and Node.js backend", "main": "src/index.js", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "vercel-prebuild": "node vercel-deploy.js", "vercel-build": "npm run vercel-prebuild && next build"}, "dependencies": {"@types/fs-extra": "11.0.4", "@types/lodash": "4.17.7", "@types/node": "20.14.12", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "autoprefixer": "10.4.19", "axios": "1.7.2", "class-variance-authority": "0.7.0", "clsx": "2.1.1", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.18.2", "framer-motion": "11.3.19", "fs-extra": "11.2.0", "groq-sdk": "0.5.0", "helmet": "^7.1.0", "lodash": "4.17.21", "lucide-react": "0.408.0", "ml-matrix": "^6.10.4", "ml-regression": "^2.0.0", "next": "14.2.5", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "openai": "^5.10.1", "playwright": "^1.42.1", "postcss": "8.4.40", "puppeteer": "^24.14.0", "puppeteer-core": "^10.1.0", "chrome-aws-lambda": "^10.1.0", "rate-limiter-flexible": "^2.4.2", "react": "18.3.1", "react-dom": "18.3.1", "sharp": "^0.33.2", "simple-statistics": "^7.8.3", "tailwindcss": "3.4.7", "technicalindicators": "^3.1.0", "tesseract.js": "^6.0.1", "tulind": "^0.8.20", "typescript": "5.5.4", "uuid": "^9.0.1", "winston": "^3.11.0", "ws": "^8.14.2", "yahoo-finance2": "^2.13.3"}, "devDependencies": {"@types/next": "^8.0.7", "eslint": "8.57.0", "eslint-config-next": "14.2.5"}, "engines": {"node": ">=18.0.0"}}