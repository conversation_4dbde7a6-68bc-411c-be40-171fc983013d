{"startTime": "2025-07-20T12:20:02.566Z", "endTime": null, "duration": 0, "tests": [], "success": false, "error": {"message": "MultiTimeframeAnalyzer is not a constructor", "stack": "TypeError: MultiTimeframeAnalyzer is not a constructor\n    at new QXBrokerOTCSignalGenerator (E:\\Ranveer\\TRADAI\\src\\core\\QXBrokerOTCSignalGenerator.js:272:39)\n    at runTest (E:\\Ranveer\\TRADAI\\tests\\qxBrokerOtcTest.js:62:23)\n    at Object.<anonymous> (E:\\Ranveer\\TRADAI\\tests\\qxBrokerOtcTest.js:266:1)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)"}}