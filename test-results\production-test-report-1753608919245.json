{"deployment": {"url": "https://tradai-g0hd87qe2-ranveer-singh-rajputs-projects.vercel.app", "timestamp": "2025-07-27T09:34:19.939Z", "tests": [{"endpoint": "/api/health", "status": 200, "responseTime": 1164, "data": {"status": "ok", "environment": "vercel", "timestamp": "2025-07-27T09:34:23.195Z", "message": "TRADAI system is running correctly", "version": "1.0.0"}, "success": true}, {"endpoint": "/api/vercel-health", "status": 200, "responseTime": 304, "data": {"status": "ok", "environment": "vercel", "timestamp": "2025-07-27T09:34:23.639Z", "message": "TRADAI system is running correctly", "version": "1.0.0"}, "success": true}, {"endpoint": "/api/system-health", "status": 200, "responseTime": 2357, "data": {"status": "WARNING", "score": 46, "timestamp": "2025-07-27T09:34:25.959Z", "components": {"dataProviders": {"twelveData": {"status": "HEALTHY", "responseTime": 171, "dataQuality": "POOR"}, "finnhub": {"status": "UNHEALTHY", "error": "Request failed with status code 403"}, "alphaVantage": {"status": "UNHEALTHY", "error": "No time series data in Alpha Vantage response"}, "polygon": {"status": "UNHEALTHY", "error": "Request failed with status code 403"}}, "apiKeys": {"twelveData": true, "finnhub": true, "alphaVantage": true, "polygon": true, "groq": true, "together": true}, "dataAvailability": {"realTimeData": false, "historicalData": true, "multiTimeframe": false, "dataFreshness": false}, "performance": {"totalRequests": 0, "successRate": 0, "avgResponseTime": 0, "cacheSize": 0, "activeProviders": 4}}, "recommendations": ["Check Finnhub API key and connectivity", "Check Alpha Vantage API key and connectivity", "Check Polygon.io API key and connectivity", "Only one healthy provider - consider fixing backup providers"], "lastHealthCheck": 1753608865350}, "success": true}]}, "forex": {"tests": [{"endpoint": "/api/forex-signal-generator", "testCase": {"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 1017, "data": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "EUR/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode.", "strictMode": true, "dataSource": "real", "timestamp": "2025-07-27T09:34:26.982Z"}, "success": true, "timestamp": "2025-07-27T09:34:24.794Z"}, {"endpoint": "/api/forex-signal-generator", "testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 288, "data": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "GBP/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode.", "strictMode": true, "dataSource": "real", "timestamp": "2025-07-27T09:34:29.320Z"}, "success": true, "timestamp": "2025-07-27T09:34:27.098Z"}, {"endpoint": "/api/forex-signal-generator", "testCase": {"pair": "USD/JPY", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 297, "data": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "USD/JPY", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode.", "strictMode": true, "dataSource": "real", "timestamp": "2025-07-27T09:34:31.618Z"}, "success": true, "timestamp": "2025-07-27T09:34:29.400Z"}, {"endpoint": "/api/forex-signal-generator", "testCase": {"pair": "AUD/USD", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 286, "data": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "AUD/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode.", "strictMode": true, "dataSource": "real", "timestamp": "2025-07-27T09:34:33.926Z"}, "success": true, "timestamp": "2025-07-27T09:34:31.694Z"}, {"endpoint": "/api/forex-signal-generator", "testCase": {"pair": "USD/CAD", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 268, "data": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "USD/CAD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode.", "strictMode": true, "dataSource": "real", "timestamp": "2025-07-27T09:34:36.210Z"}, "success": true, "timestamp": "2025-07-27T09:34:33.971Z"}, {"endpoint": "/api/forex-signal-generator", "testCase": {"pair": "EUR/USD", "trade_mode": "sniper", "risk": "1"}, "status": 200, "responseTime": 285, "data": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "EUR/USD", "trade_mode": "sniper", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode.", "strictMode": true, "dataSource": "real", "timestamp": "2025-07-27T09:34:38.485Z"}, "success": true, "timestamp": "2025-07-27T09:34:36.262Z"}, {"endpoint": "/api/forex-signal-generator", "testCase": {"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 1273, "data": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "EUR/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode.", "strictMode": true, "dataSource": "real", "timestamp": "2025-07-27T09:34:41.670Z"}, "success": true, "timestamp": "2025-07-27T09:34:39.538Z"}, {"endpoint": "/api/forex-signal-generator", "testCase": {"pair": "EUR/USD", "trade_mode": "swing", "risk": "1"}, "status": 200, "responseTime": 314, "data": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "EUR/USD", "trade_mode": "swing", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode.", "strictMode": true, "dataSource": "real", "timestamp": "2025-07-27T09:34:44.086Z"}, "success": true, "timestamp": "2025-07-27T09:34:41.855Z"}, {"endpoint": "/api/forex-signal-generator", "testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "2"}, "status": 200, "responseTime": 323, "data": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "GBP/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode.", "strictMode": true, "dataSource": "real", "timestamp": "2025-07-27T09:34:46.412Z"}, "success": true, "timestamp": "2025-07-27T09:34:44.183Z"}, {"endpoint": "/api/forex-signal-generator", "testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "3"}, "status": 200, "responseTime": 304, "data": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "GBP/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode.", "strictMode": true, "dataSource": "real", "timestamp": "2025-07-27T09:34:48.708Z"}, "success": true, "timestamp": "2025-07-27T09:34:46.495Z"}, {"endpoint": "/api/vercel-forex-signal", "testCase": {"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 1163, "data": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "EUR/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode."}, "success": true, "timestamp": "2025-07-27T09:34:49.672Z"}, {"endpoint": "/api/vercel-forex-signal", "testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 299, "data": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "GBP/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode."}, "success": true, "timestamp": "2025-07-27T09:34:51.979Z"}, {"endpoint": "/api/vercel-forex-signal", "testCase": {"pair": "USD/JPY", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 296, "data": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "USD/JPY", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode."}, "success": true, "timestamp": "2025-07-27T09:34:54.280Z"}, {"endpoint": "/api/vercel-forex-signal", "testCase": {"pair": "AUD/USD", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 295, "data": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "AUD/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode."}, "success": true, "timestamp": "2025-07-27T09:34:56.577Z"}, {"endpoint": "/api/vercel-forex-signal", "testCase": {"pair": "USD/CAD", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 279, "data": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "USD/CAD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode."}, "success": true, "timestamp": "2025-07-27T09:34:58.860Z"}, {"endpoint": "/api/vercel-forex-signal", "testCase": {"pair": "EUR/USD", "trade_mode": "sniper", "risk": "1"}, "status": 200, "responseTime": 375, "data": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "EUR/USD", "trade_mode": "sniper", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode."}, "success": true, "timestamp": "2025-07-27T09:35:01.246Z"}, {"endpoint": "/api/vercel-forex-signal", "testCase": {"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 355, "data": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "EUR/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode."}, "success": true, "timestamp": "2025-07-27T09:35:03.607Z"}, {"endpoint": "/api/vercel-forex-signal", "testCase": {"pair": "EUR/USD", "trade_mode": "swing", "risk": "1"}, "status": 200, "responseTime": 342, "data": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "EUR/USD", "trade_mode": "swing", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode."}, "success": true, "timestamp": "2025-07-27T09:35:05.956Z"}, {"endpoint": "/api/vercel-forex-signal", "testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "2"}, "status": 200, "responseTime": 340, "data": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "GBP/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode."}, "success": true, "timestamp": "2025-07-27T09:35:08.310Z"}, {"endpoint": "/api/vercel-forex-signal", "testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "3"}, "status": 200, "responseTime": 343, "data": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "GBP/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode."}, "success": true, "timestamp": "2025-07-27T09:35:10.665Z"}], "uniqueSignals": [{"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1", "signal": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "EUR/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode.", "strictMode": true, "dataSource": "real", "timestamp": "2025-07-27T09:34:26.982Z"}, "timestamp": "2025-07-27T09:34:24.794Z"}, {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "1", "signal": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "GBP/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode.", "strictMode": true, "dataSource": "real", "timestamp": "2025-07-27T09:34:29.320Z"}, "timestamp": "2025-07-27T09:34:27.098Z"}, {"pair": "USD/JPY", "trade_mode": "scalping", "risk": "1", "signal": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "USD/JPY", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode.", "strictMode": true, "dataSource": "real", "timestamp": "2025-07-27T09:34:31.618Z"}, "timestamp": "2025-07-27T09:34:29.400Z"}, {"pair": "AUD/USD", "trade_mode": "scalping", "risk": "1", "signal": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "AUD/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode.", "strictMode": true, "dataSource": "real", "timestamp": "2025-07-27T09:34:33.926Z"}, "timestamp": "2025-07-27T09:34:31.694Z"}, {"pair": "USD/CAD", "trade_mode": "scalping", "risk": "1", "signal": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "USD/CAD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode.", "strictMode": true, "dataSource": "real", "timestamp": "2025-07-27T09:34:36.210Z"}, "timestamp": "2025-07-27T09:34:33.971Z"}, {"pair": "EUR/USD", "trade_mode": "sniper", "risk": "1", "signal": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "EUR/USD", "trade_mode": "sniper", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode.", "strictMode": true, "dataSource": "real", "timestamp": "2025-07-27T09:34:38.485Z"}, "timestamp": "2025-07-27T09:34:36.262Z"}, {"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1", "signal": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "EUR/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode.", "strictMode": true, "dataSource": "real", "timestamp": "2025-07-27T09:34:41.670Z"}, "timestamp": "2025-07-27T09:34:39.538Z"}, {"pair": "EUR/USD", "trade_mode": "swing", "risk": "1", "signal": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "EUR/USD", "trade_mode": "swing", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode.", "strictMode": true, "dataSource": "real", "timestamp": "2025-07-27T09:34:44.086Z"}, "timestamp": "2025-07-27T09:34:41.855Z"}, {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "2", "signal": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "GBP/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode.", "strictMode": true, "dataSource": "real", "timestamp": "2025-07-27T09:34:46.412Z"}, "timestamp": "2025-07-27T09:34:44.183Z"}, {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "3", "signal": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "GBP/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode.", "strictMode": true, "dataSource": "real", "timestamp": "2025-07-27T09:34:48.708Z"}, "timestamp": "2025-07-27T09:34:46.496Z"}, {"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1", "signal": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "EUR/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode."}, "timestamp": "2025-07-27T09:34:49.672Z"}, {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "1", "signal": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "GBP/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode."}, "timestamp": "2025-07-27T09:34:51.979Z"}, {"pair": "USD/JPY", "trade_mode": "scalping", "risk": "1", "signal": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "USD/JPY", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode."}, "timestamp": "2025-07-27T09:34:54.280Z"}, {"pair": "AUD/USD", "trade_mode": "scalping", "risk": "1", "signal": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "AUD/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode."}, "timestamp": "2025-07-27T09:34:56.578Z"}, {"pair": "USD/CAD", "trade_mode": "scalping", "risk": "1", "signal": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "USD/CAD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode."}, "timestamp": "2025-07-27T09:34:58.860Z"}, {"pair": "EUR/USD", "trade_mode": "sniper", "risk": "1", "signal": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "EUR/USD", "trade_mode": "sniper", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode."}, "timestamp": "2025-07-27T09:35:01.246Z"}, {"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1", "signal": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "EUR/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode."}, "timestamp": "2025-07-27T09:35:03.607Z"}, {"pair": "EUR/USD", "trade_mode": "swing", "risk": "1", "signal": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "EUR/USD", "trade_mode": "swing", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode."}, "timestamp": "2025-07-27T09:35:05.956Z"}, {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "2", "signal": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "GBP/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode."}, "timestamp": "2025-07-27T09:35:08.310Z"}, {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "3", "signal": {"error": "Forex markets are closed on weekends. Demo signals available in development mode.", "pair": "GBP/USD", "trade_mode": "scalping", "message": "No high-confidence signal detected at this time. Market conditions may not be optimal for the selected trading mode."}, "timestamp": "2025-07-27T09:35:10.665Z"}], "strictModeValidation": [{"testCase": {"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": true, "value": true}, {"check": "realDataSource", "passed": true, "value": "real"}, {"check": "pair", "passed": true, "value": "EUR/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-27T09:34:24.794Z"}, {"testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": true, "value": true}, {"check": "realDataSource", "passed": true, "value": "real"}, {"check": "pair", "passed": true, "value": "GBP/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-27T09:34:27.098Z"}, {"testCase": {"pair": "USD/JPY", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": true, "value": true}, {"check": "realDataSource", "passed": true, "value": "real"}, {"check": "pair", "passed": true, "value": "USD/JPY"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-27T09:34:29.400Z"}, {"testCase": {"pair": "AUD/USD", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": true, "value": true}, {"check": "realDataSource", "passed": true, "value": "real"}, {"check": "pair", "passed": true, "value": "AUD/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-27T09:34:31.694Z"}, {"testCase": {"pair": "USD/CAD", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": true, "value": true}, {"check": "realDataSource", "passed": true, "value": "real"}, {"check": "pair", "passed": true, "value": "USD/CAD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-27T09:34:33.971Z"}, {"testCase": {"pair": "EUR/USD", "trade_mode": "sniper", "risk": "1"}, "validations": [{"check": "strictMode", "passed": true, "value": true}, {"check": "realDataSource", "passed": true, "value": "real"}, {"check": "pair", "passed": true, "value": "EUR/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-27T09:34:36.262Z"}, {"testCase": {"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": true, "value": true}, {"check": "realDataSource", "passed": true, "value": "real"}, {"check": "pair", "passed": true, "value": "EUR/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-27T09:34:39.538Z"}, {"testCase": {"pair": "EUR/USD", "trade_mode": "swing", "risk": "1"}, "validations": [{"check": "strictMode", "passed": true, "value": true}, {"check": "realDataSource", "passed": true, "value": "real"}, {"check": "pair", "passed": true, "value": "EUR/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-27T09:34:41.855Z"}, {"testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "2"}, "validations": [{"check": "strictMode", "passed": true, "value": true}, {"check": "realDataSource", "passed": true, "value": "real"}, {"check": "pair", "passed": true, "value": "GBP/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-27T09:34:44.183Z"}, {"testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "3"}, "validations": [{"check": "strictMode", "passed": true, "value": true}, {"check": "realDataSource", "passed": true, "value": "real"}, {"check": "pair", "passed": true, "value": "GBP/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-27T09:34:46.496Z"}, {"testCase": {"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "EUR/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-27T09:34:49.672Z"}, {"testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "GBP/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-27T09:34:51.979Z"}, {"testCase": {"pair": "USD/JPY", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "USD/JPY"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-27T09:34:54.280Z"}, {"testCase": {"pair": "AUD/USD", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "AUD/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-27T09:34:56.578Z"}, {"testCase": {"pair": "USD/CAD", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "USD/CAD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-27T09:34:58.860Z"}, {"testCase": {"pair": "EUR/USD", "trade_mode": "sniper", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "EUR/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-27T09:35:01.246Z"}, {"testCase": {"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "EUR/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-27T09:35:03.607Z"}, {"testCase": {"pair": "EUR/USD", "trade_mode": "swing", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "EUR/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-27T09:35:05.956Z"}, {"testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "2"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "GBP/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-27T09:35:08.310Z"}, {"testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "3"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "GBP/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-27T09:35:10.665Z"}]}, "otc": {"tests": [{"endpoint": "/api/otc-signal-generator", "testCase": {"currencyPair": "USD/PKR", "timeframe": "1m", "tradeDuration": "1"}, "status": 200, "responseTime": 753, "data": {"success": true, "requestId": "API_1753608915472_MTc1MzYw", "processingTime": 171, "signal": "PUT", "confidence": 82, "direction": "PUT", "analysis": "MACD bearish crossover + Price below MA20 < MA50 + Price at lower Bollinger Band", "qualityGrade": "B+", "riskScore": "MEDIUM", "technicalIndicators": {"rsi": 50, "macd": {"macd": -0.31231369987091284, "signal": 0.03607119120683125, "histogram": -0.34838489107774406}, "trend": "bearish", "volatility": 0.014995925943877237, "dataSource": "real_market_data"}, "strictMode": true, "metadata": {"dataSource": "real", "strictMode": true, "analysisMethod": "real_technical_indicators", "uniqueId": "USD/PKR_1m_1753608915645", "timestamp": "2025-07-27T09:35:15.645Z", "bullishStrength": 1.5, "bearishStrength": 2.5, "totalStrength": 4, "currencyPair": "USD/PKR", "timeframe": "1m", "tradeDuration": "1", "platform": "quotex"}}, "success": true, "timestamp": "2025-07-27T09:35:13.430Z"}, {"endpoint": "/api/otc-signal-generator", "testCase": {"currencyPair": "USD/PKR", "timeframe": "3m", "tradeDuration": "3"}, "status": 429, "error": "Request failed with status code 429", "success": false, "timestamp": "2025-07-27T09:35:15.738Z"}, {"endpoint": "/api/otc-signal-generator", "testCase": {"currencyPair": "USD/PKR", "timeframe": "5m", "tradeDuration": "5"}, "status": 429, "error": "Request failed with status code 429", "success": false, "timestamp": "2025-07-27T09:35:16.100Z"}, {"endpoint": "/api/otc-signal-generator", "testCase": {"currencyPair": "USD/DZD", "timeframe": "1m", "tradeDuration": "1"}, "status": 429, "error": "Request failed with status code 429", "success": false, "timestamp": "2025-07-27T09:35:16.400Z"}, {"endpoint": "/api/otc-signal-generator", "testCase": {"currencyPair": "USD/DZD", "timeframe": "3m", "tradeDuration": "3"}, "status": 429, "error": "Request failed with status code 429", "success": false, "timestamp": "2025-07-27T09:35:16.709Z"}, {"endpoint": "/api/otc-signal-generator", "testCase": {"currencyPair": "USD/DZD", "timeframe": "5m", "tradeDuration": "5"}, "status": 429, "error": "Request failed with status code 429", "success": false, "timestamp": "2025-07-27T09:35:17.016Z"}, {"endpoint": "/api/qxbroker-otc-signal", "testCase": {"currencyPair": "USD/PKR", "timeframe": "1m", "tradeDuration": "1"}, "status": 500, "error": "Request failed with status code 500", "success": false, "timestamp": "2025-07-27T09:35:17.540Z"}, {"endpoint": "/api/qxbroker-otc-signal", "testCase": {"currencyPair": "USD/PKR", "timeframe": "3m", "tradeDuration": "3"}, "status": 429, "error": "Request failed with status code 429", "success": false, "timestamp": "2025-07-27T09:35:17.832Z"}, {"endpoint": "/api/qxbroker-otc-signal", "testCase": {"currencyPair": "USD/PKR", "timeframe": "5m", "tradeDuration": "5"}, "status": 429, "error": "Request failed with status code 429", "success": false, "timestamp": "2025-07-27T09:35:18.105Z"}, {"endpoint": "/api/qxbroker-otc-signal", "testCase": {"currencyPair": "USD/DZD", "timeframe": "1m", "tradeDuration": "1"}, "status": 429, "error": "Request failed with status code 429", "success": false, "timestamp": "2025-07-27T09:35:18.457Z"}, {"endpoint": "/api/qxbroker-otc-signal", "testCase": {"currencyPair": "USD/DZD", "timeframe": "3m", "tradeDuration": "3"}, "status": 429, "error": "Request failed with status code 429", "success": false, "timestamp": "2025-07-27T09:35:18.718Z"}, {"endpoint": "/api/qxbroker-otc-signal", "testCase": {"currencyPair": "USD/DZD", "timeframe": "5m", "tradeDuration": "5"}, "status": 429, "error": "Request failed with status code 429", "success": false, "timestamp": "2025-07-27T09:35:19.241Z"}], "imageAnalysis": [], "strictModeValidation": [{"testCase": {"currencyPair": "USD/PKR", "timeframe": "1m", "tradeDuration": "1"}, "validations": [{"check": "strictMode", "passed": true, "value": true}, {"check": "realDataSource", "passed": false}], "timestamp": "2025-07-27T09:35:13.430Z"}]}, "validation": {"strictMode": true, "realDataUsage": false, "noFallbacks": true, "performanceMetrics": {"forex": {"averageResponseTime": 437, "maxResponseTime": 1273, "minResponseTime": 268, "successRate": 100}, "otc": {"averageResponseTime": 753, "maxResponseTime": 753, "minResponseTime": 753, "successRate": 8}}}}