#!/usr/bin/env node

/**
 * Production Setup Script for AI Binary Trading Bot
 * 
 * This script sets up the trading bot for production deployment without user interaction
 */

const fs = require('fs-extra');
const path = require('path');

class ProductionSetup {
  constructor() {
    this.config = {};
  }
  
  async run() {
    console.log('🚀 AI Binary Trading Bot Production Setup');
    console.log('=========================================\n');
    
    try {
      await this.checkSystemRequirements();
      await this.createDirectories();
      await this.setupConfiguration();
      await this.finalizeSetup();
      
      console.log('\n✅ Production setup completed successfully!');
      console.log('\nProduction deployment ready!');
      
    } catch (error) {
      console.error('\n❌ Production setup failed:', error.message);
      process.exit(1);
    }
  }
  
  async checkSystemRequirements() {
    console.log('🔍 Checking system requirements...\n');
    
    // Check Node.js version
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion < 18) {
      throw new Error(`Node.js 18+ required. Current version: ${nodeVersion}`);
    }
    console.log(`✅ Node.js version: ${nodeVersion}`);
    
    // Chrome/Chromium is not needed for production web deployment
    console.log('✅ Production environment - browser automation not required');
    
    // Check available disk space
    const stats = await fs.stat('.');
    console.log('✅ Disk space check passed');
    
    console.log('');
  }
  
  async createDirectories() {
    console.log('📁 Creating directories...\n');
    
    const directories = [
      './logs',
      './logs/screenshots',
      './data',
      './config',
      './backups'
    ];
    
    for (const dir of directories) {
      await fs.ensureDir(dir);
      console.log(`✅ Created: ${dir}`);
    }
    
    console.log('');
  }
  
  async setupConfiguration() {
    console.log('⚙️  Setting up configuration...\n');
    
    // Check if .env already exists
    if (await fs.pathExists('.env')) {
      console.log('✅ Using existing .env file');
      return;
    }
    
    // Create a default .env file for production
    const envContent = `# AI Binary Trading Bot Configuration
# Generated by production setup on ${new Date().toISOString()}

# ===========================================
# API KEYS (Set these in Vercel Environment Variables)
# ===========================================
TWELVE_DATA_API_KEY=${process.env.TWELVE_DATA_API_KEY || ''}
GROQ_API_KEY=${process.env.GROQ_API_KEY || ''}
TOGETHER_API_KEY=${process.env.TOGETHER_API_KEY || ''}

# ===========================================
# TRADING CONFIGURATION
# ===========================================
CURRENCY_PAIR=USD/EUR
TRADE_AMOUNT=10
MIN_CONFIDENCE=75
PAPER_TRADING=true
SIGNAL_ONLY=true
AI_PROVIDER=groq
TRADE_EXECUTION=false

# ===========================================
# PRODUCTION CONFIGURATION
# ===========================================
NODE_ENV=production
LOG_LEVEL=info
SELENIUM_HEADLESS=true
WEBSOCKET_PORT=8080
ACCOUNT_BALANCE=1000
TIMEFRAME=2min
MAX_DAILY_TRADES=50
MAX_CONSECUTIVE_LOSSES=3
DATABASE_PATH=./data/tradai.db
ENABLE_AI_LEARNING=true
USE_KELLY_CRITERION=true
MAX_RISK_PER_TRADE=2.0
DAILY_LOSS_LIMIT=10.0
DATA_COLLECTION_INTERVAL=1
SIGNAL_GENERATION_INTERVAL=2
HISTORICAL_LOOKBACK=100
MIN_CANDLES_FOR_ANALYSIS=20
`;
    
    await fs.writeFile('.env', envContent);
    console.log('✅ Default .env file created');
    console.log('');
  }
  
  async finalizeSetup() {
    console.log('🎯 Finalizing production setup...\n');
    
    // Create initial log entry
    try {
      await fs.ensureFile('./logs/trading.log');
      const logEntry = `${new Date().toISOString()} [info] Production setup completed successfully\n`;
      await fs.appendFile('./logs/trading.log', logEntry);
      console.log('✅ Logging system initialized');
    } catch (error) {
      console.log('⚠️  Logging initialization warning:', error.message);
    }
    
    // Create a basic trading config if it doesn't exist
    try {
      await fs.ensureFile('./config/trading.json');
      const configExists = await fs.pathExists('./config/trading.json');
      const stats = await fs.stat('./config/trading.json');
      
      if (!configExists || stats.size === 0) {
        const tradingConfig = {
          currency_pair: "USD/EUR",
          trade_amount: 10,
          min_confidence: 75,
          paper_trading: true,
          signal_only: true,
          ai_provider: "groq",
          max_daily_trades: 50,
          max_consecutive_losses: 3,
          risk_management: {
            max_risk_per_trade: 2.0,
            daily_loss_limit: 10.0,
            use_kelly_criterion: true
          },
          technical_analysis: {
            timeframe: "2min",
            historical_lookback: 100,
            min_candles_for_analysis: 20
          }
        };
        
        await fs.writeJson('./config/trading.json', tradingConfig, { spaces: 2 });
        console.log('✅ Trading configuration created');
      } else {
        console.log('✅ Using existing trading configuration');
      }
    } catch (error) {
      console.log('⚠️  Trading configuration warning:', error.message);
    }
    
    console.log('');
  }
}

// Run setup if called directly
if (require.main === module) {
  const setup = new ProductionSetup();
  setup.run().catch(console.error);
}

module.exports = { ProductionSetup };