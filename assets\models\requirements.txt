# Advanced AI Model Training Requirements
# Professional-grade dependencies for large-scale candle prediction

# Core ML/AI libraries
tensorflow>=2.13.0
tensorflowjs>=4.10.0
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0

# Technical analysis
TA-Lib>=0.4.25
yfinance>=0.2.18

# Data processing
requests>=2.31.0
aiohttp>=3.8.0

# Visualization
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.15.0

# Utilities
tqdm>=4.65.0
joblib>=1.3.0

# Development tools
jupyter>=1.0.0
ipykernel>=6.25.0

# Note: Install TA-Lib separately:
# Windows: Download from https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib
# Linux/Mac: sudo apt-get install ta-lib (or brew install ta-lib)
# Then: pip install TA-Lib
matplotlib>=3.5.0
seaborn>=0.11.0
jupyter>=1.0.0

# Optional for advanced features
plotly>=5.0.0
dash>=2.0.0