{"timestamp": "2025-07-17T18:05:51.560Z", "summary": {"total": 6, "working": 2, "failed": 4}, "details": [{"name": "Twelve Data", "status": "WORKING", "statusCode": 200}, {"name": "Groq", "status": "WORKING", "statusCode": 200}, {"name": "Together AI", "status": "ERROR", "error": "Request failed with status code 401", "statusCode": 401, "responseData": {"id": "o3TmtwH-2kFHot-960baba78a44a6ff", "error": {"message": "Invalid API key provided. You can find your API key at https://api.together.xyz/settings/api-keys.", "type": "invalid_request_error", "param": null, "code": "invalid_api_key"}}}, {"name": "OpenRouter", "status": "ERROR", "error": "Request failed with status code 401", "statusCode": 401, "responseData": {"error": {"message": "No auth credentials found", "code": 401}}}, {"name": "Fireworks", "status": "ERROR", "error": "Request failed with status code 404", "statusCode": 404, "responseData": {"error": {"code": "NOT_FOUND", "message": "Model not found, inaccessible, and/or not deployed", "requestId": "cmpl-09c51029169a41709d8d526f08e39698", "param": "model"}}}, {"name": "DeepInfra", "status": "ERROR", "error": "Request failed with status code 404", "statusCode": 404, "responseData": {"detail": {"error": "Model is not available"}}}]}