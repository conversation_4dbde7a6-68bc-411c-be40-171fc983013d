{"version": 2, "name": "tradai-signal-generator", "framework": "nextjs", "buildCommand": "npm run vercel-build", "devCommand": "npm run dev", "installCommand": "npm install", "env": {"NODE_ENV": "production", "VERCEL": "1", "OPENCV4NODEJS_DISABLE_AUTOBUILD": "1"}, "functions": {"pages/api/forex-signal-generator.ts": {"maxDuration": 300}, "pages/api/professional-chart-analysis.js": {"maxDuration": 300}, "pages/api/vercel-health.js": {"maxDuration": 10}, "pages/api/otc-signal-generator.js": {"maxDuration": 300}, "pages/api/multi-timeframe-analysis.js": {"maxDuration": 300}, "pages/api/otc-signal-generator/health.js": {"maxDuration": 30}}, "regions": ["iad1"], "routes": [{"src": "/api/professional-chart-analysis", "dest": "/api/professional-chart-analysis"}, {"src": "/otc-generator", "dest": "/otc-generator"}, {"src": "/api/forex-signal-generator", "dest": "/api/forex-signal-generator"}, {"src": "/forex-signal-generator", "dest": "/forex-signal-generator"}, {"src": "/api/health", "dest": "/api/vercel-health"}, {"src": "/api/otc-signal-generator", "dest": "/api/otc-signal-generator"}, {"src": "/api/multi-timeframe-analysis", "dest": "/api/multi-timeframe-analysis"}, {"src": "/otc-signal-generator", "dest": "/otc-signal-generator"}]}