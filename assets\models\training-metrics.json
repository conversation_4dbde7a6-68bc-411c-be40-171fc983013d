{"model_performance": {"finalAccuracy": 0.5255101919174194, "trainingHistory": {"val_loss": [0.7039287090301514, 0.7022346258163452, 0.7000102400779724, 0.6981511116027832, 0.6984100937843323, 0.6999791860580444, 0.7006564140319824, 0.699022650718689, 0.6999555230140686, 0.7075206637382507, 0.7047041058540344, 0.6991792917251587, 0.7000171542167664, 0.7004125714302063, 0.701991081237793, 0.7048386335372925, 0.7015498280525208, 0.7023755311965942, 0.6996903419494629, 0.6970148682594299, 0.7010670900344849, 0.7004820108413696, 0.6978441476821899, 0.6961397528648376, 0.6934265494346619, 0.7005025148391724, 0.7017149329185486, 0.6994637846946716, 0.6992751955986023, 0.700019121170044, 0.7014389634132385, 0.7020732760429382, 0.6980125904083252, 0.7001112699508667, 0.6983688473701477], "val_acc": [0.4897959232330322, 0.48469385504722595, 0.47959181666374207, 0.4897959232330322, 0.47959181666374207, 0.4948979616165161, 0.4897959232330322, 0.48469385504722595, 0.4897959232330322, 0.4693877398967743, 0.44387754797935486, 0.48469385504722595, 0.47959181666374207, 0.5, 0.48469385504722595, 0.5051020383834839, 0.5102040767669678, 0.5, 0.5051020383834839, 0.5051020383834839, 0.5102040767669678, 0.5102040767669678, 0.5051020383834839, 0.5102040767669678, 0.5255101919174194, 0.5102040767669678, 0.4897959232330322, 0.5, 0.4948979616165161, 0.5051020383834839, 0.5102040767669678, 0.4897959232330322, 0.5102040767669678, 0.5102040767669678, 0.5255101919174194], "loss": [0.8091201782226562, 0.779559314250946, 0.7429713010787964, 0.7502160668373108, 0.7451983094215393, 0.7222591042518616, 0.7295666933059692, 0.7178712487220764, 0.7107945084571838, 0.7118317484855652, 0.7033288478851318, 0.7069842219352722, 0.6980518698692322, 0.6904804110527039, 0.7042452692985535, 0.6878872513771057, 0.6931715607643127, 0.7074851393699646, 0.6952326893806458, 0.6978577971458435, 0.7063337564468384, 0.6848507523536682, 0.6984924674034119, 0.6873747706413269, 0.6967768669128418, 0.6930800676345825, 0.6873731017112732, 0.6943197846412659, 0.6908692121505737, 0.6881400346755981, 0.6841539144515991, 0.6919236183166504, 0.6827401518821716, 0.6868032813072205, 0.6804205775260925], "acc": [0.5261813402175903, 0.5274584889411926, 0.5159642100334167, 0.501915693283081, 0.501915693283081, 0.5210728049278259, 0.5236270427703857, 0.5300127863883972, 0.5261813402175903, 0.532567024230957, 0.5210728049278259, 0.5300127863883972, 0.5466155409812927, 0.5351213216781616, 0.5300127863883972, 0.5619412660598755, 0.5504469871520996, 0.5261813402175903, 0.5415070056915283, 0.50957852602005, 0.5440613031387329, 0.5670498013496399, 0.540229856967926, 0.5619412660598755, 0.5300127863883972, 0.5453384518623352, 0.5759897828102112, 0.5300127863883972, 0.5530012845993042, 0.5376756191253662, 0.5466155409812927, 0.5389527082443237, 0.5581098198890686, 0.5568326711654663, 0.5721583366394043]}, "epochs": 50, "trainSamples": 783, "valSamples": 196, "evaluation": {"accuracy": 0.5255102040816326, "precision": 0.5652173913043478, "recall": 0.38235294117647056, "f1Score": 0.45614035087719296, "confusionMatrix": {"tp": 39, "tn": 64, "fp": 30, "fn": 63}, "winRates": {"0.6": 0.5555555555555556, "0.7": 0.6428571428571429, "0.8": 0, "0.9": 0}, "totalSamples": 196}}, "training_config": {"epochs": 50, "batch_size": 32, "learning_rate": 0.001, "architecture": [128, 64, 32, 2], "optimizer": "adam", "loss": "sparseCategoricalCrossentropy"}, "data_info": {"total_samples": 1000, "quality_samples": 979, "features_per_sample": 24, "train_val_split": "80/20"}, "export_timestamp": "2025-06-30T07:31:35.179Z"}