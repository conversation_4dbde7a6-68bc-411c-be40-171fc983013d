{"manifest_version": 3, "name": "AI Candle Sniper - Binary Options Predictor", "version": "1.0.0", "description": "AI-powered multi-timeframe binary options candle prediction with professional trading logic", "permissions": ["activeTab", "storage", "scripting", "background"], "host_permissions": ["https://*/*", "http://*/*"], "action": {"default_popup": "popup.html", "default_title": "AI Candle Sniper", "default_icon": {"16": "assets/icon16.png", "32": "assets/icon32.png", "48": "assets/icon48.png", "128": "assets/icon128.png"}}, "background": {"service_worker": "background-new.js", "type": "module"}, "content_scripts": [{"matches": ["*://*.quotex.io/*", "*://*.olymptrade.com/*", "*://*.iqoption.com/*", "*://*.binomo.com/*", "*://*.pocketoption.com/*"], "js": ["content.js"], "run_at": "document_idle"}, {"matches": ["*://*.quotex.io/*", "*://*.olymptrade.com/*", "*://*.iqoption.com/*", "*://*.binomo.com/*", "*://*.pocketoption.com/*"], "js": ["otc-content.js"], "run_at": "document_idle"}], "web_accessible_resources": [{"resources": ["assets/*", "assets/models/*", "utils/*", "utils/quotex-extractor.js", "utils/pocket-option-extractor.js", "utils/otc-data-extractor.js", "utils/otc-error-handler.js", "utils/otc-data-validator.js", "utils/otc-test-suite.js", "utils/otc-status-monitor.js", "utils/otc-historical-data.js", "utils/real-time-analyzer.js", "utils/indicators.js", "utils/patterns.js", "utils/tensorflow-ai-model.js", "utils/ai-signal-engine.js", "utils/auto-trade-engine.js", "background/*", "background/otc-handler.js", "ai-integration.js", "popup-sniper.html", "popup-sniper.css", "popup-sniper.js", "popup-otc.html", "popup-otc.css", "popup-otc.js", "otc-content.js"], "matches": ["<all_urls>"]}], "icons": {"16": "assets/icon16.png", "32": "assets/icon32.png", "48": "assets/icon48.png", "128": "assets/icon128.png"}}