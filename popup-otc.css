/* AI Candle Sniper - OTC Mode Popup Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
  --otc-primary: #6a1b9a;
  --otc-secondary: #9c27b0;
  --otc-accent: #e1bee7;
  --otc-text: #f3e5f5;
  --otc-background: #4a148c;
  --otc-card: #7b1fa2;
  --otc-border: #ba68c8;
  --otc-success: #00c853;
  --otc-warning: #ffd600;
  --otc-danger: #ff1744;
  --otc-neutral: #78909c;
  
  /* Status colors */
  --status-active: #00c853;
  --status-inactive: #ff1744;
  --status-pending: #ffd600;
}

body.otc-mode {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #ffffff;
    width: 400px;
    min-height: 600px;
    overflow-x: hidden;
}

.container {
    padding: 16px;
    max-height: 600px;
    overflow-y: auto;
}

/* Header Styles */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #2a2a4e;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
}

.title h1 {
    font-size: 18px;
    font-weight: 700;
    color: #00d4ff;
    margin: 0;
}

.subtitle {
    font-size: 12px;
    color: #ff6b35;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.version {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
}

/* Status Panel */
.status-panel {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
}

.status-panel > div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.status-panel > div:last-child {
    margin-bottom: 0;
}

.status-label {
    font-size: 13px;
    color: #b0b0b0;
    font-weight: 500;
}

.status-value {
    font-size: 13px;
    font-weight: 600;
}

.status-value.active {
    color: #00ff88;
}

.status-value.inactive {
    color: #ff4757;
}

.status-value.warning {
    color: #ffa502;
}

/* Weekend Notice */
.weekend-notice {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
    text-align: center;
    font-size: 12px;
    font-weight: 500;
    animation: pulse 2s infinite;
}

.weekend-notice.hidden {
    display: none;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* Control Panel */
.control-panel {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(45deg, #00d4ff, #0099cc);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(45deg, #00b8e6, #0088bb);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
}

.btn-danger {
    background: linear-gradient(45deg, #ff4757, #cc3a47);
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: linear-gradient(45deg, #e63946, #bb2d3b);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
}

.btn-success {
    background: linear-gradient(45deg, #00ff88, #00cc6a);
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: linear-gradient(45deg, #00e67a, #00b359);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 255, 136, 0.3);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

/* Selection Panel */
.selection-panel {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
}

.selection-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.selection-row:last-child {
    margin-bottom: 0;
}

.selection-label {
    font-size: 13px;
    color: #b0b0b0;
    font-weight: 500;
    min-width: 80px;
}

.selection-dropdown {
    flex: 1;
    margin-left: 12px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: #ffffff;
    font-size: 12px;
    cursor: pointer;
}

.selection-dropdown:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.selection-dropdown option {
    background: #1a1a2e;
    color: #ffffff;
}

/* Signal Panel */
.signal-panel {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
    backdrop-filter: blur(10px);
}

.signal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.signal-title {
    font-size: 14px;
    font-weight: 600;
    color: #00d4ff;
}

.signal-confidence {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    background: linear-gradient(45deg, #00ff88, #00cc6a);
    color: white;
}

.signal-direction {
    text-align: center;
    padding: 16px;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 16px;
}

.direction-call {
    background: linear-gradient(45deg, #00ff88, #00cc6a);
    color: white;
}

.direction-put {
    background: linear-gradient(45deg, #ff4757, #cc3a47);
    color: white;
}

.direction-neutral {
    background: rgba(255, 255, 255, 0.1);
    color: #b0b0b0;
}

.signal-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 12px;
}

.signal-detail {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
}

.signal-detail span:first-child {
    color: #b0b0b0;
}

.signal-detail span:last-child {
    color: #ffffff;
    font-weight: 600;
}

.signal-reason {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    padding: 8px;
    font-size: 11px;
    color: #d0d0d0;
    line-height: 1.4;
}

/* Section Styles */
.section {
    margin-bottom: 20px;
}

.section h3 {
    font-size: 14px;
    font-weight: 600;
    color: #00d4ff;
    margin-bottom: 12px;
    padding-bottom: 6px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Trade History */
.trade-history {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    max-height: 120px;
    overflow-y: auto;
}

.trade-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    font-size: 11px;
}

.trade-item:last-child {
    border-bottom: none;
}

.trade-pair {
    font-weight: 600;
    color: #ffffff;
}

.trade-direction {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 9px;
    font-weight: 600;
    text-transform: uppercase;
}

.trade-direction.call {
    background: #00ff88;
    color: #000;
}

.trade-direction.put {
    background: #ff4757;
    color: #fff;
}

.trade-time {
    color: #b0b0b0;
    font-size: 10px;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.metric {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 12px;
    text-align: center;
}

.metric-label {
    display: block;
    font-size: 11px;
    color: #b0b0b0;
    margin-bottom: 4px;
}

.metric-value {
    display: block;
    font-size: 16px;
    font-weight: 700;
    color: #ffffff;
}

.metric-value.positive {
    color: #00ff88;
}

.metric-value.negative {
    color: #ff4757;
}

/* System Status */
.system-status {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 12px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 12px;
}

.status-item:last-child {
    margin-bottom: 0;
}

.status-component {
    color: #b0b0b0;
}

.component-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
}

.component-status.healthy {
    background: #00ff88;
    color: #000;
}

.component-status.warning {
    background: #ffa502;
    color: #000;
}

.component-status.error {
    background: #ff4757;
    color: #fff;
}

.component-status.unknown {
    background: rgba(255, 255, 255, 0.2);
    color: #b0b0b0;
}

/* Debug Panel */
.debug-panel {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
}

.debug-content {
    margin-bottom: 12px;
}

.debug-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px;
    font-size: 11px;
}

.debug-label {
    color: #b0b0b0;
}

.debug-actions {
    display: flex;
    gap: 6px;
}

.debug-actions .btn {
    flex: 1;
    padding: 6px 8px;
    font-size: 10px;
}

/* Footer */
.footer {
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-bottom: 8px;
}

.link-button {
    background: none;
    border: none;
    color: #00d4ff;
    font-size: 11px;
    cursor: pointer;
    text-decoration: underline;
    padding: 0;
}

.link-button:hover {
    color: #00b8e6;
}

.footer-info {
    text-align: center;
    font-size: 10px;
    color: #b0b0b0;
}

/* Notification Styles */
#notification-container {
    position: fixed;
    top: 16px;
    right: 16px;
    z-index: 1000;
}

.notification {
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 8px;
    max-width: 300px;
    font-size: 12px;
    animation: slideIn 0.3s ease;
}

.notification.success {
    border-left: 4px solid #00ff88;
}

.notification.error {
    border-left: 4px solid #ff4757;
}

.notification.warning {
    border-left: 4px solid #ffa502;
}

.notification.info {
    border-left: 4px solid #00d4ff;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-left: 4px solid #00d4ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

.loading-text {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Scrollbar Styles */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Hidden class */
.hidden {
    display: none !important;
}

/* Utility classes */
.text-center {
    text-align: center;
}

.text-success {
    color: #00ff88;
}

.text-error {
    color: #ff4757;
}

.text-warning {
    color: #ffa502;
}

.text-info {
    color: #00d4ff;
}

.otc-badge {
  background-color: var(--otc-accent);
  color: var(--otc-background);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  margin-left: 8px;
}

.otc-mode header {
  border-bottom: 1px solid var(--otc-border);
}

.otc-mode .mode-btn {
  background-color: var(--otc-neutral);
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.otc-mode .mode-btn.active {
  background-color: var(--otc-accent);
  color: var(--otc-background);
  font-weight: bold;
}

/* Status indicator styles */
.status-panel {
  display: flex;
  flex-direction: column;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 12px;
  margin: 10px 0;
}

.status-indicator, .broker-info, .last-update {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.status-value {
  font-weight: bold;
}

.status-value.active {
  color: var(--status-active);
}

.status-value.inactive {
  color: var(--status-inactive);
}

.status-value.pending {
  color: var(--status-pending);
}

/* Control panel styles */
.control-panel {
  display: flex;
  justify-content: space-between;
  margin: 15px 0;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--otc-primary);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--otc-secondary);
}

.btn-danger {
  background-color: var(--otc-danger);
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #d50000;
}

.btn-success {
  background-color: var(--otc-success);
  color: white;
}

.btn-success:hover:not(:disabled) {
  background-color: #00a844;
}

.btn-secondary {
  background-color: var(--otc-neutral);
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #546e7a;
}

/* Asset and timeframe selection */
.selection-panel {
  display: flex;
  flex-direction: column;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 12px;
  margin: 10px 0;
}

.selection-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.selection-row:last-child {
  margin-bottom: 0;
}

.selection-label {
  font-weight: bold;
  margin-right: 10px;
  min-width: 80px;
}

.selection-dropdown {
  flex: 1;
  padding: 6px 10px;
  border: 1px solid var(--otc-accent);
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.9);
  color: var(--otc-dark);
}

.selection-dropdown:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Signal display */
.signal-panel {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
  border-left: 4px solid var(--otc-primary);
}

.signal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.signal-title {
  font-weight: bold;
  font-size: 16px;
}

.signal-confidence {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.confidence-high {
  background-color: var(--otc-success);
  color: white;
}

.confidence-medium {
  background-color: var(--otc-warning);
  color: var(--otc-dark);
}

.confidence-low {
  background-color: var(--otc-danger);
  color: white;
}

.signal-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 10px;
}

.signal-detail {
  display: flex;
  justify-content: space-between;
}

.signal-direction {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  padding: 10px;
  border-radius: 6px;
  margin: 10px 0;
}

.direction-up {
  background-color: var(--otc-success);
  color: white;
}

.direction-down {
  background-color: var(--otc-danger);
  color: white;
}

.direction-neutral {
  background-color: var(--otc-neutral);
  color: white;
}

/* Trade history */
.trade-history {
  max-height: 200px;
  overflow-y: auto;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 10px;
  margin: 15px 0;
}

.trade-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  margin-bottom: 5px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  font-size: 12px;
}

.trade-item:last-child {
  margin-bottom: 0;
}

.trade-pair {
  font-weight: bold;
}

.trade-direction {
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: bold;
}

.trade-up {
  background-color: var(--otc-success);
  color: white;
}

.trade-down {
  background-color: var(--otc-danger);
  color: white;
}

.trade-time {
  color: var(--otc-light);
  font-size: 10px;
}

/* Weekend notice */
.weekend-notice {
  background-color: var(--otc-info);
  color: var(--otc-dark);
  padding: 12px;
  border-radius: 8px;
  margin: 10px 0;
  text-align: center;
  font-weight: bold;
}

.weekend-notice.hidden {
  display: none;
}

/* Notifications */
.notification {
  position: fixed;
  top: 10px;
  right: 10px;
  padding: 12px 16px;
  border-radius: 6px;
  color: white;
  font-weight: bold;
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
}

.notification.success {
  background-color: var(--otc-success);
}

.notification.error {
  background-color: var(--otc-danger);
}

.notification.warning {
  background-color: var(--otc-warning);
  color: var(--otc-dark);
}

.notification.info {
  background-color: var(--otc-info);
  color: var(--otc-dark);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Loading states */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Responsive design */
@media (max-width: 400px) {
  .control-panel {
    flex-direction: column;
  }
  
  .btn {
    margin-bottom: 8px;
    width: 100%;
  }
  
  .signal-details {
    grid-template-columns: 1fr;
  }
}

.otc-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin: 15px 0;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.pair-selector, .timeframe-selector {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.pair-selector label, .timeframe-selector label {
  font-size: 12px;
  font-weight: bold;
}

.pair-selector select, .timeframe-selector select {
  padding: 8px;
  border-radius: 4px;
  border: 1px solid var(--otc-border);
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
}

.otc-mode .primary-btn {
  background-color: var(--otc-accent);
  color: var(--otc-background);
  border: none;
  padding: 10px;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 10px;
}

.otc-mode .primary-btn:hover {
  background-color: white;
}

.otc-mode .primary-btn:disabled {
  background-color: var(--otc-neutral);
  cursor: not-allowed;
  opacity: 0.7;
}

.signal-container {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
  transition: all 0.3s ease;
}

.signal-container.hidden {
  display: none;
}

.signal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.signal-header h2 {
  margin: 0;
  font-size: 16px;
}

.timestamp {
  font-size: 12px;
  color: var(--otc-accent);
}

.signal-pair {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

#signalPair {
  font-size: 18px;
  font-weight: bold;
}

.timeframe-badge {
  background-color: var(--otc-accent);
  color: var(--otc-background);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.signal-direction {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15px;
}

.direction-indicator {
  padding: 10px 20px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 10px;
  min-width: 100px;
  text-align: center;
}

.direction-indicator.up {
  background-color: var(--otc-success);
  color: white;
}

.direction-indicator.down {
  background-color: var(--otc-danger);
  color: white;
}

.direction-indicator.neutral {
  background-color: var(--otc-neutral);
  color: white;
}

.confidence-meter {
  width: 100%;
  height: 8px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  position: relative;
  margin-bottom: 5px;
}

.meter-fill {
  height: 100%;
  border-radius: 4px;
  background-color: var(--otc-accent);
  transition: width 0.5s ease;
}

#confidenceValue {
  font-size: 12px;
  color: var(--otc-accent);
}

.signal-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
}

.detail-label {
  font-weight: bold;
  font-size: 12px;
}

.detail-value {
  font-size: 12px;
}

.signal-actions {
  display: flex;
  justify-content: center;
}

.trade-btn {
  background-color: var(--otc-success);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.trade-btn:hover {
  background-color: #00e676;
}

.trade-btn:disabled {
  background-color: var(--otc-neutral);
  cursor: not-allowed;
  opacity: 0.7;
}

.data-status {
  display: flex;
  justify-content: space-between;
  margin: 15px 0;
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  font-size: 12px;
}

.data-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.data-label {
  font-weight: bold;
  color: var(--otc-accent);
}

.extraction-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.secondary-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
}

.secondary-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.secondary-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.otc-mode footer {
  border-top: 1px solid var(--otc-border);
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  font-size: 12px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--otc-neutral);
}

.status-dot.active {
  background-color: var(--otc-success);
}

.status-dot.error {
  background-color: var(--otc-danger);
}

.status-dot.warning {
  background-color: var(--otc-warning);
}

.broker-info {
  font-weight: bold;
}