/* ===== AI Trading Sniper - Professional Dark Theme ===== */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto', sans-serif;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  color: #e0e0e0;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: hidden;
}

.container {
  width: 380px;
  max-height: 600px;
  overflow-y: auto;
  background: rgba(15, 15, 35, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 255, 136, 0.1);
  border-radius: 12px;
  position: relative;
}

/* ===== HEADER ===== */
.header {
  background: linear-gradient(135deg, #1e1e3f 0%, #2a2a5a 100%);
  padding: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(0, 255, 136, 0.2);
  border-radius: 12px 12px 0 0;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  font-size: 20px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.logo-text h1 {
  font-size: 14px;
  font-weight: 700;
  background: linear-gradient(45deg, #00ff88, #00d4ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
}

.tagline {
  font-size: 9px;
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.status-display {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  font-weight: 600;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #00ff88;
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

.scan-timer {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
  font-weight: 500;
}

/* ===== SECTIONS ===== */
.section {
  padding: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.section-header h3 {
  font-size: 12px;
  font-weight: 600;
  color: #00ff88;
  margin: 0;
}

/* ===== MARKET SECTION ===== */
.market-section {
  background: rgba(0, 255, 136, 0.02);
  border: 1px solid rgba(0, 255, 136, 0.1);
  border-radius: 8px;
  margin: 8px;
  padding: 12px;
}

.market-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.pair-name {
  font-size: 16px;
  font-weight: 700;
  color: #00ff88;
}

.pair-platform {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.market-health {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.trend-indicator, .volatility-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 10px;
}

.trend-label, .vol-label {
  color: rgba(255, 255, 255, 0.6);
}

.trend-value {
  font-weight: 600;
  color: #00ff88;
}

.vol-value {
  font-weight: 600;
  color: #00d4ff;
}

.last-analysis {
  display: flex;
  justify-content: space-between;
  font-size: 9px;
  color: rgba(255, 255, 255, 0.5);
}

/* ===== SIGNAL SECTION ===== */
.signal-section {
  background: linear-gradient(135deg, rgba(0, 255, 136, 0.05) 0%, rgba(0, 212, 255, 0.05) 100%);
  border: 2px solid rgba(0, 255, 136, 0.3);
  border-radius: 10px;
  margin: 8px;
  padding: 15px;
  position: relative;
  overflow: hidden;
}

.signal-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #00ff88, #00d4ff);
  animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.signal-card {
  position: relative;
  z-index: 1;
}

.signal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.signal-direction {
  font-size: 24px;
  font-weight: 900;
  color: #00ff88;
  text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
  animation: glow-text 2s infinite alternate;
}

@keyframes glow-text {
  from { text-shadow: 0 0 10px rgba(0, 255, 136, 0.5); }
  to { text-shadow: 0 0 20px rgba(0, 255, 136, 0.8); }
}

.signal-confidence {
  text-align: right;
}

.confidence-value {
  font-size: 18px;
  font-weight: 700;
  color: #00d4ff;
  margin-bottom: 4px;
}

.confidence-bar {
  width: 80px;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.confidence-fill {
  height: 100%;
  background: linear-gradient(90deg, #00ff88, #00d4ff);
  border-radius: 2px;
  transition: width 0.5s ease;
}

.signal-details {
  margin-bottom: 12px;
}

.signal-timeframe {
  font-size: 12px;
  font-weight: 600;
  color: #00d4ff;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.signal-reasons {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  padding: 8px;
}

.reason-item {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2px;
  line-height: 1.3;
}

.signal-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.signal-timer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  padding: 6px 10px;
}

.timer-label {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
}

.timer-countdown {
  font-size: 14px;
  font-weight: 700;
  color: #ff6b6b;
  font-family: 'Courier New', monospace;
}

/* ===== DISCIPLINE SECTION ===== */
.discipline-section {
  background: rgba(255, 107, 107, 0.02);
  border: 1px solid rgba(255, 107, 107, 0.1);
  border-radius: 8px;
  margin: 8px;
}

.discipline-status {
  font-size: 10px;
  color: #00ff88;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.discipline-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-bottom: 10px;
}

.stat-card {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  padding: 8px;
  text-align: center;
}

.stat-label {
  font-size: 9px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 12px;
  font-weight: 700;
  color: #00d4ff;
}

.stat-value.streak {
  color: #00ff88;
}

.psychology-coaching {
  background: linear-gradient(135deg, rgba(0, 255, 136, 0.05) 0%, rgba(0, 212, 255, 0.05) 100%);
  border-radius: 6px;
  padding: 8px;
}

.coaching-message {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  font-style: italic;
}

/* ===== RISK SECTION ===== */
.risk-section {
  background: rgba(255, 165, 2, 0.02);
  border: 1px solid rgba(255, 165, 2, 0.1);
  border-radius: 8px;
  margin: 8px;
}

.capital-input {
  margin-bottom: 10px;
}

.capital-input label {
  display: block;
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.capital-input input {
  width: 100%;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  padding: 6px 8px;
  color: #fff;
  font-size: 12px;
}

.capital-input input:focus {
  outline: none;
  border-color: #00ff88;
  box-shadow: 0 0 0 2px rgba(0, 255, 136, 0.2);
}

.risk-suggestion {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  padding: 10px;
}

.suggested-trade {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.trade-label {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
}

.trade-amount {
  font-size: 16px;
  font-weight: 700;
  color: #ffa502;
}

.trade-risk {
  font-size: 9px;
  color: rgba(255, 255, 255, 0.5);
}

.risk-adjustment {
  font-size: 9px;
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
}

/* ===== TIMEFRAMES GRID ===== */
.timeframes-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.timeframe-card {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 8px;
  transition: all 0.3s ease;
}

.timeframe-card:hover {
  border-color: rgba(0, 255, 136, 0.3);
  transform: translateY(-1px);
}

.tf-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.tf-label {
  font-size: 11px;
  font-weight: 600;
  color: #00d4ff;
}

.tf-strength {
  font-size: 9px;
  color: rgba(255, 255, 255, 0.6);
}

.tf-trend {
  font-size: 10px;
  font-weight: 600;
  color: #00ff88;
  margin-bottom: 2px;
}

.tf-signals {
  font-size: 9px;
  color: rgba(255, 255, 255, 0.5);
}

/* ===== TABS SYSTEM ===== */
.tab-nav {
  display: flex;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 12px;
}

.tab-item {
  flex: 1;
  padding: 8px 12px;
  font-size: 10px;
  font-weight: 600;
  text-align: center;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tab-item.active {
  color: #00ff88;
  border-bottom: 2px solid #00ff88;
}

.tab-item:hover {
  color: #00d4ff;
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

/* ===== INDICATORS LIST ===== */
.indicators-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.indicator-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 6px 8px;
}

.indicator-name {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.indicator-value {
  font-size: 11px;
  font-weight: 600;
  color: #00d4ff;
  font-family: 'Courier New', monospace;
}

.indicator-status {
  font-size: 9px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 2px 6px;
  border-radius: 10px;
}

.indicator-status.bullish {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
}

.indicator-status.bearish {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
}

.indicator-status.neutral {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.6);
}

.indicator-status.normal {
  background: rgba(0, 212, 255, 0.2);
  color: #00d4ff;
}

/* ===== PATTERNS LIST ===== */
.patterns-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.pattern-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 6px 8px;
  border-left: 3px solid transparent;
}

.pattern-item.bullish {
  border-left-color: #00ff88;
}

.pattern-item.bearish {
  border-left-color: #ff6b6b;
}

.pattern-item.neutral {
  border-left-color: #ffa502;
}

.pattern-name {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.pattern-timeframe {
  font-size: 9px;
  color: #00d4ff;
  font-weight: 600;
}

.pattern-strength {
  font-size: 9px;
  color: rgba(255, 255, 255, 0.6);
}

/* ===== JOURNAL ===== */
.journal-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-bottom: 12px;
}

.journal-stat {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  padding: 8px;
  text-align: center;
}

.win-rate {
  color: #00ff88 !important;
}

.journal-entries {
  max-height: 120px;
  overflow-y: auto;
}

.journal-entry {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 6px 8px;
  margin-bottom: 4px;
  font-size: 10px;
  border-left: 3px solid transparent;
}

.journal-entry.win {
  border-left-color: #00ff88;
}

.journal-entry.loss {
  border-left-color: #ff6b6b;
}

.entry-time {
  color: rgba(255, 255, 255, 0.6);
  font-family: 'Courier New', monospace;
}

.entry-pair {
  color: #00d4ff;
  font-weight: 600;
}

.entry-direction {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.entry-result {
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.journal-entry.win .entry-result {
  color: #00ff88;
}

.journal-entry.loss .entry-result {
  color: #ff6b6b;
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  min-height: 32px;
}

.btn-primary {
  background: linear-gradient(135deg, #00ff88 0%, #00d4ff 100%);
  color: #000;
  box-shadow: 0 2px 10px rgba(0, 255, 136, 0.3);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 255, 136, 0.4);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  color: #00d4ff;
  border: 1px solid rgba(0, 212, 255, 0.3);
}

.btn-outline:hover {
  background: rgba(0, 212, 255, 0.1);
  border-color: #00d4ff;
}

.btn-take-trade {
  background: linear-gradient(135deg, #00ff88 0%, #00d4ff 100%);
  color: #000;
  flex: 1;
  font-weight: 700;
}

.btn-skip-signal {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.2);
  flex: 1;
}

.btn-xs {
  padding: 4px 8px;
  font-size: 9px;
  min-height: 24px;
}

.btn-icon {
  font-size: 12px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* ===== CONTROL SECTION ===== */
.control-buttons {
  margin-bottom: 12px;
}

.control-buttons .btn {
  width: 100%;
  margin-bottom: 6px;
}

.control-settings {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.setting-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
}

.setting-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 14px;
  height: 14px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  position: relative;
  transition: all 0.3s ease;
}

.setting-label input[type="checkbox"]:checked + .checkmark {
  background: linear-gradient(135deg, #00ff88 0%, #00d4ff 100%);
  border-color: #00ff88;
}

.setting-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #000;
  font-size: 10px;
  font-weight: 700;
}

.setting-label select {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: #fff;
  font-size: 10px;
  padding: 4px 6px;
}

.setting-label select:focus {
  outline: none;
  border-color: #00ff88;
}

/* ===== FOOTER ===== */
.footer {
  background: rgba(0, 0, 0, 0.3);
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 0 0 12px 12px;
}

.footer-left, .footer-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.version {
  font-size: 9px;
  color: rgba(255, 255, 255, 0.4);
  font-weight: 500;
}

.ai-status {
  font-size: 9px;
  color: #00ff88;
  font-weight: 600;
}

.system-health {
  font-size: 9px;
  color: #00d4ff;
  font-weight: 500;
}

/* ===== MODALS ===== */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: linear-gradient(135deg, #1e1e3f 0%, #2a2a5a 100%);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: 12px;
  padding: 20px;
  max-width: 300px;
  width: 90%;
  text-align: center;
}

.modal-header h3 {
  font-size: 14px;
  color: #ff6b6b;
  margin-bottom: 12px;
}

.alert-message {
  font-size: 12px;
  color: #fff;
  margin-bottom: 8px;
  line-height: 1.4;
}

.alert-coaching {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
  margin-bottom: 16px;
}

.modal-actions {
  display: flex;
  justify-content: center;
}

/* ===== LOADING OVERLAY ===== */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(15, 15, 35, 0.95);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 999;
  border-radius: 12px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #00ff88;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

/* ===== SCROLLBAR STYLING ===== */
.container::-webkit-scrollbar {
  width: 4px;
}

.container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.container::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 136, 0.3);
  border-radius: 2px;
}

.container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 136, 0.5);
}

/* ===== RESPONSIVE BREAKPOINTS ===== */
@media (max-height: 500px) {
  .container {
    max-height: 90vh;
  }
  
  .section {
    padding: 8px;
  }
  
  .header {
    padding: 8px 12px;
  }
}

/* ===== ANIMATION CLASSES ===== */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* ===== UTILITY CLASSES ===== */
.text-bullish {
  color: #00ff88 !important;
}

.text-bearish {
  color: #ff6b6b !important;
}

.text-neutral {
  color: #ffa502 !important;
}

.bg-success {
  background: rgba(0, 255, 136, 0.1) !important;
}

.bg-danger {
  background: rgba(255, 107, 107, 0.1) !important;
}

.bg-warning {
  background: rgba(255, 165, 2, 0.1) !important;
}

.hidden {
  display: none !important;
}

.visible {
  display: block !important;
}

.no-data {
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
  font-size: 11px;
  padding: 20px;
  font-style: italic;
}

/* ===== HOVER EFFECTS ===== */
.hover-glow:hover {
  box-shadow: 0 0 15px rgba(0, 255, 136, 0.3);
  transform: translateY(-1px);
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* ===== FOCUS STATES ===== */
button:focus,
input:focus,
select:focus {
  outline: 2px solid rgba(0, 255, 136, 0.5);
  outline-offset: 2px;
}

/* ===== PRINT STYLES ===== */
@media print {
  .container {
    box-shadow: none;
    border: 1px solid #ccc;
  }
  
  .loading-overlay {
    display: none !important;
  }
}