{"modelTopology": {"class_name": "Sequential", "config": {"name": "sequential_1", "layers": [{"class_name": "<PERSON><PERSON>", "config": {"units": 128, "activation": "relu", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null, "name": "dense_input", "trainable": true, "batch_input_shape": [null, 24], "dtype": "float32"}}, {"class_name": "Dropout", "config": {"rate": 0.3, "noise_shape": null, "seed": null, "name": "dropout_1", "trainable": true}}, {"class_name": "BatchNormalization", "config": {"axis": -1, "momentum": 0.99, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"class_name": "Zeros", "config": {}}, "gamma_initializer": {"class_name": "Ones", "config": {}}, "moving_mean_initializer": {"class_name": "Zeros", "config": {}}, "moving_variance_initializer": {"class_name": "Ones", "config": {}}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "name": "batch_norm_1", "trainable": true}}, {"class_name": "<PERSON><PERSON>", "config": {"units": 64, "activation": "relu", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null, "name": "dense_hidden_1", "trainable": true}}, {"class_name": "Dropout", "config": {"rate": 0.2, "noise_shape": null, "seed": null, "name": "dropout_2", "trainable": true}}, {"class_name": "BatchNormalization", "config": {"axis": -1, "momentum": 0.99, "epsilon": 0.001, "center": true, "scale": true, "beta_initializer": {"class_name": "Zeros", "config": {}}, "gamma_initializer": {"class_name": "Ones", "config": {}}, "moving_mean_initializer": {"class_name": "Zeros", "config": {}}, "moving_variance_initializer": {"class_name": "Ones", "config": {}}, "beta_regularizer": null, "gamma_regularizer": null, "beta_constraint": null, "gamma_constraint": null, "name": "batch_norm_2", "trainable": true}}, {"class_name": "<PERSON><PERSON>", "config": {"units": 32, "activation": "relu", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1, "mode": "fan_avg", "distribution": "uniform", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null, "name": "dense_hidden_2", "trainable": true}}, {"class_name": "Dropout", "config": {"rate": 0.1, "noise_shape": null, "seed": null, "name": "dropout_3", "trainable": true}}, {"class_name": "<PERSON><PERSON>", "config": {"units": 2, "activation": "softmax", "use_bias": true, "kernel_initializer": {"class_name": "VarianceScaling", "config": {"scale": 1, "mode": "fan_avg", "distribution": "normal", "seed": null}}, "bias_initializer": {"class_name": "Zeros", "config": {}}, "kernel_regularizer": null, "bias_regularizer": null, "activity_regularizer": null, "kernel_constraint": null, "bias_constraint": null, "name": "dense_output", "trainable": true}}]}, "keras_version": "tfjs-layers 4.15.0", "backend": "tensor_flow.js"}, "format": "layers-model", "generatedBy": "TensorFlow.js tfjs-layers v4.15.0", "convertedBy": null, "weightsManifest": [{"paths": ["./trading-model.weights.bin"], "weights": [{"name": "dense_input/kernel", "shape": [24, 128], "dtype": "float32"}, {"name": "dense_input/bias", "shape": [128], "dtype": "float32"}, {"name": "batch_norm_1/gamma", "shape": [128], "dtype": "float32"}, {"name": "batch_norm_1/beta", "shape": [128], "dtype": "float32"}, {"name": "dense_hidden_1/kernel", "shape": [128, 64], "dtype": "float32"}, {"name": "dense_hidden_1/bias", "shape": [64], "dtype": "float32"}, {"name": "batch_norm_2/gamma", "shape": [64], "dtype": "float32"}, {"name": "batch_norm_2/beta", "shape": [64], "dtype": "float32"}, {"name": "dense_hidden_2/kernel", "shape": [64, 32], "dtype": "float32"}, {"name": "dense_hidden_2/bias", "shape": [32], "dtype": "float32"}, {"name": "dense_output/kernel", "shape": [32, 2], "dtype": "float32"}, {"name": "dense_output/bias", "shape": [2], "dtype": "float32"}, {"name": "batch_norm_1/moving_mean", "shape": [128], "dtype": "float32"}, {"name": "batch_norm_1/moving_variance", "shape": [128], "dtype": "float32"}, {"name": "batch_norm_2/moving_mean", "shape": [64], "dtype": "float32"}, {"name": "batch_norm_2/moving_variance", "shape": [64], "dtype": "float32"}]}]}