{"deployment": {"url": "https://tradai-frwxb92q4-ranveer-singh-rajputs-projects.vercel.app", "timestamp": "2025-07-23T06:00:49.495Z", "tests": [{"endpoint": "/api/health", "status": 200, "responseTime": 2441, "data": {"status": "ok", "environment": "vercel", "timestamp": "2025-07-23T06:00:51.225Z", "message": "TRADAI system is running correctly", "version": "1.0.0"}, "success": true}, {"endpoint": "/api/vercel-health", "status": 200, "responseTime": 601, "data": {"status": "ok", "environment": "vercel", "timestamp": "2025-07-23T06:00:51.875Z", "message": "TRADAI system is running correctly", "version": "1.0.0"}, "success": true}, {"endpoint": "/api/system-health", "status": 200, "responseTime": 2149, "data": {"status": "WARNING", "score": 65, "timestamp": "2025-07-23T06:00:54.062Z", "components": {"dataProviders": {"twelveData": {"status": "HEALTHY", "responseTime": 160, "dataQuality": "GOOD"}, "finnhub": {"status": "UNHEALTHY", "error": "Request failed with status code 403"}, "alphaVantage": {"status": "UNHEALTHY", "error": "No time series data in Alpha Vantage response"}, "polygon": {"status": "UNHEALTHY", "error": "Request failed with status code 403"}}, "apiKeys": {"twelveData": true, "finnhub": true, "alphaVantage": true, "polygon": true, "groq": true, "together": true}, "dataAvailability": {"realTimeData": true, "historicalData": true, "multiTimeframe": true, "dataFreshness": true}, "performance": {"totalRequests": 0, "successRate": 0, "avgResponseTime": 0, "cacheSize": 0, "activeProviders": 4}}, "recommendations": ["Check Finnhub API key and connectivity", "Check Alpha Vantage API key and connectivity", "Check Polygon.io API key and connectivity", "Only one healthy provider - consider fixing backup providers"], "lastHealthCheck": 1753250453765}, "success": true}]}, "forex": {"tests": [{"endpoint": "/api/forex-signal-generator", "testCase": {"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 1236, "data": {"pair": "EUR/USD", "trade_mode": "scalping", "error": "No clear signal detected with sufficient confidence", "message": "Current market conditions do not meet the criteria for a high-confidence signal"}, "success": true, "timestamp": "2025-07-23T06:00:55.932Z"}, {"endpoint": "/api/forex-signal-generator", "testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 740, "data": {"pair": "GBP/USD", "trade_mode": "scalping", "error": "No clear signal detected with sufficient confidence", "message": "Current market conditions do not meet the criteria for a high-confidence signal"}, "success": true, "timestamp": "2025-07-23T06:00:58.685Z"}, {"endpoint": "/api/forex-signal-generator", "testCase": {"pair": "USD/JPY", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 1319, "data": {"pair": "USD/JPY", "trade_mode": "scalping", "error": "No clear signal detected with sufficient confidence", "message": "Current market conditions do not meet the criteria for a high-confidence signal"}, "success": true, "timestamp": "2025-07-23T06:01:02.011Z"}, {"endpoint": "/api/forex-signal-generator", "testCase": {"pair": "AUD/USD", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 711, "data": {"pair": "AUD/USD", "trade_mode": "scalping", "error": "No clear signal detected with sufficient confidence", "message": "Current market conditions do not meet the criteria for a high-confidence signal"}, "success": true, "timestamp": "2025-07-23T06:01:04.727Z"}, {"endpoint": "/api/forex-signal-generator", "testCase": {"pair": "USD/CAD", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 441, "data": {"pair": "USD/CAD", "trade_mode": "scalping", "error": "No clear signal detected with sufficient confidence", "message": "Current market conditions do not meet the criteria for a high-confidence signal"}, "success": true, "timestamp": "2025-07-23T06:01:07.184Z"}, {"endpoint": "/api/forex-signal-generator", "testCase": {"pair": "EUR/USD", "trade_mode": "sniper", "risk": "1"}, "status": 200, "responseTime": 453, "data": {"pair": "EUR/USD", "trade_mode": "sniper", "error": "No clear signal detected with sufficient confidence", "message": "Current market conditions do not meet the criteria for a high-confidence signal"}, "success": true, "timestamp": "2025-07-23T06:01:09.643Z"}, {"endpoint": "/api/forex-signal-generator", "testCase": {"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 441, "data": {"pair": "EUR/USD", "trade_mode": "scalping", "error": "No clear signal detected with sufficient confidence", "message": "Current market conditions do not meet the criteria for a high-confidence signal"}, "success": true, "timestamp": "2025-07-23T06:01:12.098Z"}, {"endpoint": "/api/forex-signal-generator", "testCase": {"pair": "EUR/USD", "trade_mode": "swing", "risk": "1"}, "status": 200, "responseTime": 443, "data": {"pair": "EUR/USD", "trade_mode": "swing", "error": "No clear signal detected with sufficient confidence", "message": "Current market conditions do not meet the criteria for a high-confidence signal"}, "success": true, "timestamp": "2025-07-23T06:01:14.556Z"}, {"endpoint": "/api/forex-signal-generator", "testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "2"}, "status": 200, "responseTime": 394, "data": {"pair": "GBP/USD", "trade_mode": "scalping", "error": "No clear signal detected with sufficient confidence", "message": "Current market conditions do not meet the criteria for a high-confidence signal"}, "success": true, "timestamp": "2025-07-23T06:01:16.954Z"}, {"endpoint": "/api/forex-signal-generator", "testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "3"}, "status": 200, "responseTime": 411, "data": {"pair": "GBP/USD", "trade_mode": "scalping", "error": "No clear signal detected with sufficient confidence", "message": "Current market conditions do not meet the criteria for a high-confidence signal"}, "success": true, "timestamp": "2025-07-23T06:01:19.372Z"}, {"endpoint": "/api/vercel-forex-signal", "testCase": {"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 6484, "data": {"pair": "EUR/USD", "trade_type": "SELL", "entry": 1.03921, "stop_loss": 1.04021, "take_profit": 1.03681, "rr_ratio": 2.4, "confidence": 85, "timeframe": "5M", "trade_mode": "scalping", "reason": "Medium: MACD bear + EMA 20/50 bear + RSI<50 + near support", "risk_per_trade": "1%", "execution_platform": "MT5"}, "success": true, "timestamp": "2025-07-23T06:01:27.870Z"}, {"endpoint": "/api/vercel-forex-signal", "testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 6693, "data": {"pair": "GBP/USD", "trade_type": "SELL", "entry": 1.0539, "stop_loss": 1.0549, "take_profit": 1.0514, "rr_ratio": 2.5, "confidence": 85, "timeframe": "5M", "trade_mode": "scalping", "reason": "Medium: MACD bear + EMA 20/50 bull + RSI>50 + near resistance", "risk_per_trade": "1%", "execution_platform": "MT5"}, "success": true, "timestamp": "2025-07-23T06:01:36.572Z"}, {"endpoint": "/api/vercel-forex-signal", "testCase": {"pair": "USD/JPY", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 6290, "data": {"pair": "USD/JPY", "trade_type": "SELL", "entry": 1.049, "stop_loss": 1.139, "take_profit": 0.859, "rr_ratio": 2.11, "confidence": 85, "timeframe": "5M", "trade_mode": "scalping", "reason": "Medium: MACD bear + EMA 20/50 bear + RSI<50 + near support", "risk_per_trade": "1%", "execution_platform": "MT5"}, "success": true, "timestamp": "2025-07-23T06:01:44.866Z"}, {"endpoint": "/api/vercel-forex-signal", "testCase": {"pair": "AUD/USD", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 5970, "data": {"pair": "AUD/USD", "trade_type": "SELL", "entry": 1.04914, "stop_loss": 1.05004, "take_profit": 1.04714, "rr_ratio": 2.22, "confidence": 85, "timeframe": "5M", "trade_mode": "scalping", "reason": "Moderate: MACD bear + EMA 20/50 bear + RSI<50", "risk_per_trade": "1%", "execution_platform": "MT5"}, "success": true, "timestamp": "2025-07-23T06:01:52.851Z"}, {"endpoint": "/api/vercel-forex-signal", "testCase": {"pair": "USD/CAD", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 4952, "data": {"pair": "USD/CAD", "trade_type": "SELL", "entry": 1.04939, "stop_loss": 1.05039, "take_profit": 1.04709, "rr_ratio": 2.3, "confidence": 85, "timeframe": "5M", "trade_mode": "scalping", "reason": "Medium: MACD bear + EMA 20/50 bear + RSI<50 + near support", "risk_per_trade": "1%", "execution_platform": "MT5"}, "success": true, "timestamp": "2025-07-23T06:01:59.814Z"}, {"endpoint": "/api/vercel-forex-signal", "testCase": {"pair": "EUR/USD", "trade_mode": "sniper", "risk": "1"}, "status": 200, "responseTime": 1678, "data": {"pair": "EUR/USD", "trade_type": "SELL", "entry": 1.17305, "stop_loss": 1.17345, "take_profit": 1.17225, "rr_ratio": 2, "confidence": 75, "timeframe": "1M", "trade_mode": "sniper", "reason": "Medium: Fallback analysis + price momentum + RSI oversold + below EMA20 + MACD negative", "risk_per_trade": "1%", "execution_platform": "MT5"}, "success": true, "timestamp": "2025-07-23T06:02:03.501Z"}, {"endpoint": "/api/vercel-forex-signal", "testCase": {"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1"}, "status": 200, "responseTime": 6572, "data": {"pair": "EUR/USD", "trade_type": "SELL", "entry": 1.17305, "stop_loss": 1.17405, "take_profit": 1.17105, "rr_ratio": 2, "confidence": 85, "timeframe": "5M", "trade_mode": "scalping", "reason": "Medium: MACD bear + EMA 20/50 bear + RSI<50 + near support", "risk_per_trade": "1%", "execution_platform": "MT5"}, "success": true, "timestamp": "2025-07-23T06:02:12.078Z"}, {"endpoint": "/api/vercel-forex-signal", "testCase": {"pair": "EUR/USD", "trade_mode": "swing", "risk": "1"}, "status": 200, "responseTime": 19977, "data": {"pair": "EUR/USD", "trade_type": "SELL", "entry": 1.17305, "stop_loss": 1.17525, "take_profit": 1.16475, "rr_ratio": 3.77, "confidence": 90, "timeframe": "30M", "trade_mode": "swing", "reason": "Medium: Fallback analysis + price momentum + below EMA20 + MACD negative + Multi-timeframe alignment + Fibonacci levels + Volume confirmation", "risk_per_trade": "1%", "execution_platform": "MT5"}, "success": true, "timestamp": "2025-07-23T06:02:34.061Z"}, {"endpoint": "/api/vercel-forex-signal", "testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "2"}, "status": 200, "responseTime": 6655, "data": {"pair": "GBP/USD", "trade_type": "SELL", "entry": 1.35259, "stop_loss": 1.35369, "take_profit": 1.35079, "rr_ratio": 1.64, "confidence": 85, "timeframe": "5M", "trade_mode": "scalping", "reason": "Medium: MACD bear + EMA 20/50 bear + RSI<50 + near support", "risk_per_trade": "2%", "execution_platform": "MT5"}, "success": true, "timestamp": "2025-07-23T06:02:42.720Z"}, {"endpoint": "/api/vercel-forex-signal", "testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "3"}, "status": 200, "responseTime": 5272, "data": {"pair": "GBP/USD", "trade_type": "SELL", "entry": 1.35259, "stop_loss": 1.35349, "take_profit": 1.35009, "rr_ratio": 2.78, "confidence": 85, "timeframe": "5M", "trade_mode": "scalping", "reason": "Medium: MACD bear + EMA 20/50 bear + RSI<50 + near support", "risk_per_trade": "3%", "execution_platform": "MT5"}, "success": true, "timestamp": "2025-07-23T06:02:50.008Z"}], "uniqueSignals": [{"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1", "signal": {"pair": "EUR/USD", "trade_mode": "scalping", "error": "No clear signal detected with sufficient confidence", "message": "Current market conditions do not meet the criteria for a high-confidence signal"}, "timestamp": "2025-07-23T06:00:55.932Z"}, {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "1", "signal": {"pair": "GBP/USD", "trade_mode": "scalping", "error": "No clear signal detected with sufficient confidence", "message": "Current market conditions do not meet the criteria for a high-confidence signal"}, "timestamp": "2025-07-23T06:00:58.685Z"}, {"pair": "USD/JPY", "trade_mode": "scalping", "risk": "1", "signal": {"pair": "USD/JPY", "trade_mode": "scalping", "error": "No clear signal detected with sufficient confidence", "message": "Current market conditions do not meet the criteria for a high-confidence signal"}, "timestamp": "2025-07-23T06:01:02.011Z"}, {"pair": "AUD/USD", "trade_mode": "scalping", "risk": "1", "signal": {"pair": "AUD/USD", "trade_mode": "scalping", "error": "No clear signal detected with sufficient confidence", "message": "Current market conditions do not meet the criteria for a high-confidence signal"}, "timestamp": "2025-07-23T06:01:04.728Z"}, {"pair": "USD/CAD", "trade_mode": "scalping", "risk": "1", "signal": {"pair": "USD/CAD", "trade_mode": "scalping", "error": "No clear signal detected with sufficient confidence", "message": "Current market conditions do not meet the criteria for a high-confidence signal"}, "timestamp": "2025-07-23T06:01:07.184Z"}, {"pair": "EUR/USD", "trade_mode": "sniper", "risk": "1", "signal": {"pair": "EUR/USD", "trade_mode": "sniper", "error": "No clear signal detected with sufficient confidence", "message": "Current market conditions do not meet the criteria for a high-confidence signal"}, "timestamp": "2025-07-23T06:01:09.643Z"}, {"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1", "signal": {"pair": "EUR/USD", "trade_mode": "scalping", "error": "No clear signal detected with sufficient confidence", "message": "Current market conditions do not meet the criteria for a high-confidence signal"}, "timestamp": "2025-07-23T06:01:12.098Z"}, {"pair": "EUR/USD", "trade_mode": "swing", "risk": "1", "signal": {"pair": "EUR/USD", "trade_mode": "swing", "error": "No clear signal detected with sufficient confidence", "message": "Current market conditions do not meet the criteria for a high-confidence signal"}, "timestamp": "2025-07-23T06:01:14.556Z"}, {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "2", "signal": {"pair": "GBP/USD", "trade_mode": "scalping", "error": "No clear signal detected with sufficient confidence", "message": "Current market conditions do not meet the criteria for a high-confidence signal"}, "timestamp": "2025-07-23T06:01:16.954Z"}, {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "3", "signal": {"pair": "GBP/USD", "trade_mode": "scalping", "error": "No clear signal detected with sufficient confidence", "message": "Current market conditions do not meet the criteria for a high-confidence signal"}, "timestamp": "2025-07-23T06:01:19.372Z"}, {"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1", "signal": {"pair": "EUR/USD", "trade_type": "SELL", "entry": 1.03921, "stop_loss": 1.04021, "take_profit": 1.03681, "rr_ratio": 2.4, "confidence": 85, "timeframe": "5M", "trade_mode": "scalping", "reason": "Medium: MACD bear + EMA 20/50 bear + RSI<50 + near support", "risk_per_trade": "1%", "execution_platform": "MT5"}, "timestamp": "2025-07-23T06:01:27.870Z"}, {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "1", "signal": {"pair": "GBP/USD", "trade_type": "SELL", "entry": 1.0539, "stop_loss": 1.0549, "take_profit": 1.0514, "rr_ratio": 2.5, "confidence": 85, "timeframe": "5M", "trade_mode": "scalping", "reason": "Medium: MACD bear + EMA 20/50 bull + RSI>50 + near resistance", "risk_per_trade": "1%", "execution_platform": "MT5"}, "timestamp": "2025-07-23T06:01:36.572Z"}, {"pair": "USD/JPY", "trade_mode": "scalping", "risk": "1", "signal": {"pair": "USD/JPY", "trade_type": "SELL", "entry": 1.049, "stop_loss": 1.139, "take_profit": 0.859, "rr_ratio": 2.11, "confidence": 85, "timeframe": "5M", "trade_mode": "scalping", "reason": "Medium: MACD bear + EMA 20/50 bear + RSI<50 + near support", "risk_per_trade": "1%", "execution_platform": "MT5"}, "timestamp": "2025-07-23T06:01:44.866Z"}, {"pair": "AUD/USD", "trade_mode": "scalping", "risk": "1", "signal": {"pair": "AUD/USD", "trade_type": "SELL", "entry": 1.04914, "stop_loss": 1.05004, "take_profit": 1.04714, "rr_ratio": 2.22, "confidence": 85, "timeframe": "5M", "trade_mode": "scalping", "reason": "Moderate: MACD bear + EMA 20/50 bear + RSI<50", "risk_per_trade": "1%", "execution_platform": "MT5"}, "timestamp": "2025-07-23T06:01:52.851Z"}, {"pair": "USD/CAD", "trade_mode": "scalping", "risk": "1", "signal": {"pair": "USD/CAD", "trade_type": "SELL", "entry": 1.04939, "stop_loss": 1.05039, "take_profit": 1.04709, "rr_ratio": 2.3, "confidence": 85, "timeframe": "5M", "trade_mode": "scalping", "reason": "Medium: MACD bear + EMA 20/50 bear + RSI<50 + near support", "risk_per_trade": "1%", "execution_platform": "MT5"}, "timestamp": "2025-07-23T06:01:59.814Z"}, {"pair": "EUR/USD", "trade_mode": "sniper", "risk": "1", "signal": {"pair": "EUR/USD", "trade_type": "SELL", "entry": 1.17305, "stop_loss": 1.17345, "take_profit": 1.17225, "rr_ratio": 2, "confidence": 75, "timeframe": "1M", "trade_mode": "sniper", "reason": "Medium: Fallback analysis + price momentum + RSI oversold + below EMA20 + MACD negative", "risk_per_trade": "1%", "execution_platform": "MT5"}, "timestamp": "2025-07-23T06:02:03.501Z"}, {"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1", "signal": {"pair": "EUR/USD", "trade_type": "SELL", "entry": 1.17305, "stop_loss": 1.17405, "take_profit": 1.17105, "rr_ratio": 2, "confidence": 85, "timeframe": "5M", "trade_mode": "scalping", "reason": "Medium: MACD bear + EMA 20/50 bear + RSI<50 + near support", "risk_per_trade": "1%", "execution_platform": "MT5"}, "timestamp": "2025-07-23T06:02:12.078Z"}, {"pair": "EUR/USD", "trade_mode": "swing", "risk": "1", "signal": {"pair": "EUR/USD", "trade_type": "SELL", "entry": 1.17305, "stop_loss": 1.17525, "take_profit": 1.16475, "rr_ratio": 3.77, "confidence": 90, "timeframe": "30M", "trade_mode": "swing", "reason": "Medium: Fallback analysis + price momentum + below EMA20 + MACD negative + Multi-timeframe alignment + Fibonacci levels + Volume confirmation", "risk_per_trade": "1%", "execution_platform": "MT5"}, "timestamp": "2025-07-23T06:02:34.061Z"}, {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "2", "signal": {"pair": "GBP/USD", "trade_type": "SELL", "entry": 1.35259, "stop_loss": 1.35369, "take_profit": 1.35079, "rr_ratio": 1.64, "confidence": 85, "timeframe": "5M", "trade_mode": "scalping", "reason": "Medium: MACD bear + EMA 20/50 bear + RSI<50 + near support", "risk_per_trade": "2%", "execution_platform": "MT5"}, "timestamp": "2025-07-23T06:02:42.720Z"}, {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "3", "signal": {"pair": "GBP/USD", "trade_type": "SELL", "entry": 1.35259, "stop_loss": 1.35349, "take_profit": 1.35009, "rr_ratio": 2.78, "confidence": 85, "timeframe": "5M", "trade_mode": "scalping", "reason": "Medium: MACD bear + EMA 20/50 bear + RSI<50 + near support", "risk_per_trade": "3%", "execution_platform": "MT5"}, "timestamp": "2025-07-23T06:02:50.008Z"}], "strictModeValidation": [{"testCase": {"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "EUR/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-23T06:00:55.933Z"}, {"testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "GBP/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-23T06:00:58.685Z"}, {"testCase": {"pair": "USD/JPY", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "USD/JPY"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-23T06:01:02.011Z"}, {"testCase": {"pair": "AUD/USD", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "AUD/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-23T06:01:04.728Z"}, {"testCase": {"pair": "USD/CAD", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "USD/CAD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-23T06:01:07.184Z"}, {"testCase": {"pair": "EUR/USD", "trade_mode": "sniper", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "EUR/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-23T06:01:09.643Z"}, {"testCase": {"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "EUR/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-23T06:01:12.098Z"}, {"testCase": {"pair": "EUR/USD", "trade_mode": "swing", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "EUR/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-23T06:01:14.556Z"}, {"testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "2"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "GBP/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-23T06:01:16.954Z"}, {"testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "3"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "GBP/USD"}, {"check": "trade_type", "passed": false}, {"check": "entry", "passed": false}, {"check": "stop_loss", "passed": false}, {"check": "take_profit", "passed": false}, {"check": "confidence", "passed": false}], "timestamp": "2025-07-23T06:01:19.372Z"}, {"testCase": {"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "EUR/USD"}, {"check": "trade_type", "passed": true, "value": "SELL"}, {"check": "entry", "passed": true, "value": 1.03921}, {"check": "stop_loss", "passed": true, "value": 1.04021}, {"check": "take_profit", "passed": true, "value": 1.03681}, {"check": "confidence", "passed": true, "value": 85}], "timestamp": "2025-07-23T06:01:27.871Z"}, {"testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "GBP/USD"}, {"check": "trade_type", "passed": true, "value": "SELL"}, {"check": "entry", "passed": true, "value": 1.0539}, {"check": "stop_loss", "passed": true, "value": 1.0549}, {"check": "take_profit", "passed": true, "value": 1.0514}, {"check": "confidence", "passed": true, "value": 85}], "timestamp": "2025-07-23T06:01:36.572Z"}, {"testCase": {"pair": "USD/JPY", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "USD/JPY"}, {"check": "trade_type", "passed": true, "value": "SELL"}, {"check": "entry", "passed": true, "value": 1.049}, {"check": "stop_loss", "passed": true, "value": 1.139}, {"check": "take_profit", "passed": true, "value": 0.859}, {"check": "confidence", "passed": true, "value": 85}], "timestamp": "2025-07-23T06:01:44.866Z"}, {"testCase": {"pair": "AUD/USD", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "AUD/USD"}, {"check": "trade_type", "passed": true, "value": "SELL"}, {"check": "entry", "passed": true, "value": 1.04914}, {"check": "stop_loss", "passed": true, "value": 1.05004}, {"check": "take_profit", "passed": true, "value": 1.04714}, {"check": "confidence", "passed": true, "value": 85}], "timestamp": "2025-07-23T06:01:52.851Z"}, {"testCase": {"pair": "USD/CAD", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "USD/CAD"}, {"check": "trade_type", "passed": true, "value": "SELL"}, {"check": "entry", "passed": true, "value": 1.04939}, {"check": "stop_loss", "passed": true, "value": 1.05039}, {"check": "take_profit", "passed": true, "value": 1.04709}, {"check": "confidence", "passed": true, "value": 85}], "timestamp": "2025-07-23T06:01:59.814Z"}, {"testCase": {"pair": "EUR/USD", "trade_mode": "sniper", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "EUR/USD"}, {"check": "trade_type", "passed": true, "value": "SELL"}, {"check": "entry", "passed": true, "value": 1.17305}, {"check": "stop_loss", "passed": true, "value": 1.17345}, {"check": "take_profit", "passed": true, "value": 1.17225}, {"check": "confidence", "passed": true, "value": 75}], "timestamp": "2025-07-23T06:02:03.501Z"}, {"testCase": {"pair": "EUR/USD", "trade_mode": "scalping", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "EUR/USD"}, {"check": "trade_type", "passed": true, "value": "SELL"}, {"check": "entry", "passed": true, "value": 1.17305}, {"check": "stop_loss", "passed": true, "value": 1.17405}, {"check": "take_profit", "passed": true, "value": 1.17105}, {"check": "confidence", "passed": true, "value": 85}], "timestamp": "2025-07-23T06:02:12.078Z"}, {"testCase": {"pair": "EUR/USD", "trade_mode": "swing", "risk": "1"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "EUR/USD"}, {"check": "trade_type", "passed": true, "value": "SELL"}, {"check": "entry", "passed": true, "value": 1.17305}, {"check": "stop_loss", "passed": true, "value": 1.17525}, {"check": "take_profit", "passed": true, "value": 1.16475}, {"check": "confidence", "passed": true, "value": 90}], "timestamp": "2025-07-23T06:02:34.061Z"}, {"testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "2"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "GBP/USD"}, {"check": "trade_type", "passed": true, "value": "SELL"}, {"check": "entry", "passed": true, "value": 1.35259}, {"check": "stop_loss", "passed": true, "value": 1.35369}, {"check": "take_profit", "passed": true, "value": 1.35079}, {"check": "confidence", "passed": true, "value": 85}], "timestamp": "2025-07-23T06:02:42.720Z"}, {"testCase": {"pair": "GBP/USD", "trade_mode": "scalping", "risk": "3"}, "validations": [{"check": "strictMode", "passed": false}, {"check": "realDataSource", "passed": false}, {"check": "pair", "passed": true, "value": "GBP/USD"}, {"check": "trade_type", "passed": true, "value": "SELL"}, {"check": "entry", "passed": true, "value": 1.35259}, {"check": "stop_loss", "passed": true, "value": 1.35349}, {"check": "take_profit", "passed": true, "value": 1.35009}, {"check": "confidence", "passed": true, "value": 85}], "timestamp": "2025-07-23T06:02:50.008Z"}]}, "otc": {"tests": [{"endpoint": "/api/otc-signal-generator", "testCase": {"currencyPair": "USD/PKR", "timeframe": "1m", "tradeDuration": "1"}, "status": 422, "error": "Request failed with status code 422", "success": false, "timestamp": "2025-07-23T06:02:52.973Z"}, {"endpoint": "/api/otc-signal-generator", "testCase": {"currencyPair": "USD/PKR", "timeframe": "3m", "tradeDuration": "3"}, "status": 429, "error": "Request failed with status code 429", "success": false, "timestamp": "2025-07-23T06:02:53.277Z"}, {"endpoint": "/api/otc-signal-generator", "testCase": {"currencyPair": "USD/PKR", "timeframe": "5m", "tradeDuration": "5"}, "status": 429, "error": "Request failed with status code 429", "success": false, "timestamp": "2025-07-23T06:02:53.584Z"}, {"endpoint": "/api/otc-signal-generator", "testCase": {"currencyPair": "USD/DZD", "timeframe": "1m", "tradeDuration": "1"}, "status": 429, "error": "Request failed with status code 429", "success": false, "timestamp": "2025-07-23T06:02:53.888Z"}, {"endpoint": "/api/otc-signal-generator", "testCase": {"currencyPair": "USD/DZD", "timeframe": "3m", "tradeDuration": "3"}, "status": 429, "error": "Request failed with status code 429", "success": false, "timestamp": "2025-07-23T06:02:54.196Z"}, {"endpoint": "/api/otc-signal-generator", "testCase": {"currencyPair": "USD/DZD", "timeframe": "5m", "tradeDuration": "5"}, "status": 429, "error": "Request failed with status code 429", "success": false, "timestamp": "2025-07-23T06:02:54.503Z"}, {"endpoint": "/api/qxbroker-otc-signal", "testCase": {"currencyPair": "USD/PKR", "timeframe": "1m", "tradeDuration": "1"}, "status": 500, "error": "Request failed with status code 500", "success": false, "timestamp": "2025-07-23T06:02:55.136Z"}, {"endpoint": "/api/qxbroker-otc-signal", "testCase": {"currencyPair": "USD/PKR", "timeframe": "3m", "tradeDuration": "3"}, "status": 429, "error": "Request failed with status code 429", "success": false, "timestamp": "2025-07-23T06:02:55.425Z"}, {"endpoint": "/api/qxbroker-otc-signal", "testCase": {"currencyPair": "USD/PKR", "timeframe": "5m", "tradeDuration": "5"}, "status": 429, "error": "Request failed with status code 429", "success": false, "timestamp": "2025-07-23T06:02:55.732Z"}, {"endpoint": "/api/qxbroker-otc-signal", "testCase": {"currencyPair": "USD/DZD", "timeframe": "1m", "tradeDuration": "1"}, "status": 429, "error": "Request failed with status code 429", "success": false, "timestamp": "2025-07-23T06:02:55.993Z"}, {"endpoint": "/api/qxbroker-otc-signal", "testCase": {"currencyPair": "USD/DZD", "timeframe": "3m", "tradeDuration": "3"}, "status": 429, "error": "Request failed with status code 429", "success": false, "timestamp": "2025-07-23T06:02:56.263Z"}, {"endpoint": "/api/qxbroker-otc-signal", "testCase": {"currencyPair": "USD/DZD", "timeframe": "5m", "tradeDuration": "5"}, "status": 429, "error": "Request failed with status code 429", "success": false, "timestamp": "2025-07-23T06:02:56.551Z"}], "imageAnalysis": [], "strictModeValidation": []}, "validation": {"strictMode": true, "realDataUsage": false, "noFallbacks": true, "performanceMetrics": {"forex": {"averageResponseTime": 3857, "maxResponseTime": 19977, "minResponseTime": 394, "successRate": 100}, "otc": {"averageResponseTime": 0, "maxResponseTime": 0, "minResponseTime": 0, "successRate": 0}}}}