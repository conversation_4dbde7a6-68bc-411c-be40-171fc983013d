<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Candle Sniper</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <h1>🎯 AI Candle Sniper</h1>
                <p class="version">v1.0.0 Professional</p>
            </div>
            <div class="mode-switch">
                <button id="standardModeBtn" class="mode-btn active">Standard</button>
                <button id="otcModeBtn" class="mode-btn">OTC</button>
            </div>
        </header>

        <!-- Asset Detection -->
        <section class="asset-section">
            <div class="asset-info">
                <span class="label">Current Asset:</span>
                <span id="currentAsset" class="asset-name">Detecting...</span>
            </div>
            <div class="platform-info">
                <span class="label">Platform:</span>
                <span id="currentPlatform" class="platform-name">Unknown</span>
            </div>
        </section>

        <!-- Control Panel -->
        <section class="control-panel">
            <button id="startAnalysis" class="btn btn-primary">
                <span class="btn-text">Start AI Analysis</span>
                <span class="btn-icon">🚀</span>
            </button>
            <button id="stopAnalysis" class="btn btn-secondary" style="display:none;">
                <span class="btn-text">Stop Analysis</span>
                <span class="btn-icon">⏹️</span>
            </button>
        </section>

        <!-- Signal Display -->
        <section id="signalSection" class="signal-section" style="display:none;">
            <div class="signal-header">
                <h3>📊 Latest Signal</h3>
                <div class="signal-timestamp" id="signalTime"></div>
            </div>
            
            <div class="prediction-card">
                <div class="prediction-main">
                    <div class="direction" id="prediction">
                        <span class="direction-text">-</span>
                        <span class="direction-arrow">↗️</span>
                    </div>
                    <div class="confidence">
                        <div class="confidence-label">Confidence</div>
                        <div class="confidence-value" id="confidence">--%</div>
                        <div class="confidence-bar">
                            <div class="confidence-fill" id="confidenceBar"></div>
                        </div>
                    </div>
                </div>
                
                <div class="signal-details">
                    <div class="reason" id="reason">Analyzing market conditions...</div>
                    <div class="volatility-risk">
                        <span class="volatility">Volatility: <span id="volatility">-</span></span>
                        <span class="risk">Risk: <span id="risk">-</span></span>
                    </div>
                </div>
            </div>

            <!-- Timer -->
            <div class="timer-section">
                <div class="timer-label">Entry Window</div>
                <div class="timer" id="entryTimer">--:--</div>
            </div>
        </section>

        <!-- Market Analysis -->
        <section id="analysisSection" class="analysis-section" style="display:none;">
            <div class="analysis-tabs">
                <button class="tab-btn active" data-tab="indicators">Indicators</button>
                <button class="tab-btn" data-tab="patterns">Patterns</button>
                <button class="tab-btn" data-tab="timeframes">Timeframes</button>
            </div>
            
            <div class="tab-content" id="indicators">
                <div class="indicator-grid">
                    <div class="indicator">
                        <span class="indicator-name">RSI</span>
                        <span class="indicator-value" id="rsi">--</span>
                    </div>
                    <div class="indicator">
                        <span class="indicator-name">MACD</span>
                        <span class="indicator-value" id="macd">--</span>
                    </div>
                    <div class="indicator">
                        <span class="indicator-name">EMA21</span>
                        <span class="indicator-value" id="ema21">--</span>
                    </div>
                    <div class="indicator">
                        <span class="indicator-name">Volume</span>
                        <span class="indicator-value" id="volume">--</span>
                    </div>
                </div>
            </div>
            
            <div class="tab-content" id="patterns" style="display:none;">
                <div class="patterns-list" id="patternsList">
                    <div class="no-patterns">No significant patterns detected</div>
                </div>
            </div>
            
            <div class="tab-content" id="timeframes" style="display:none;">
                <div class="timeframe-analysis" id="timeframeAnalysis">
                    <div class="timeframe-loading">Loading multi-timeframe analysis...</div>
                </div>
            </div>
        </section>

        <!-- Settings & Controls -->
        <section class="settings-section">
            <div class="settings-row">
                <label class="setting-label">
                    <input type="checkbox" id="voiceAlerts" checked>
                    <span class="checkmark"></span>
                    Voice Alerts
                </label>
                <label class="setting-label">
                    <input type="checkbox" id="autoAnalysis">
                    <span class="checkmark"></span>
                    Auto Analysis
                </label>
            </div>
            <div class="settings-row">
                <label class="setting-label">
                    Min Confidence: 
                    <select id="minConfidence">
                        <option value="60">60%</option>
                        <option value="65">65%</option>
                        <option value="70">70%</option>
                        <option value="75">75%</option>
                        <option value="80">80%</option>
                        <option value="85" selected>85%</option>
                        <option value="90">90%</option>
                    </select>
                </label>
                <button id="refreshData" class="btn-small refresh-btn">
                    <span class="refresh-icon">🔄</span>
                </button>
            </div>
            
            <!-- Auto-Trading Controls -->
            <div class="auto-trade-section">
                <div class="auto-trade-header">
                    <h4>Auto-Trading</h4>
                    <div class="auto-trade-toggle">
                        <label class="switch">
                            <input type="checkbox" id="autoTradeToggle">
                            <span class="slider round"></span>
                        </label>
                        <span id="autoTradeStatus" class="status-disabled">DISABLED</span>
                    </div>
                </div>
                <div class="auto-trade-controls">
                    <button id="emergencyStop" class="emergency-stop-btn">
                        <span class="stop-icon">🛑</span>
                        <span class="stop-text">EMERGENCY STOP</span>
                    </button>
                    <div id="autoTradeExecutionStatus" class="auto-trade-status disabled">
                        AUTO-TRADE DISABLED
                    </div>
                </div>
            </div>
            
            <!-- Trade History Section -->
            <div class="trade-history-section">
                <div class="trade-history-header">
                    <h4>Trade History</h4>
                    <button id="viewTradeHistory" class="btn btn-small">View All</button>
                </div>
                <div id="tradeStats" class="trade-stats">
                    <div class="stat-item">
                        <span class="stat-label">Today:</span>
                        <span class="stat-value">0/5</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Win Rate:</span>
                        <span class="stat-value">0.0%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">W/L:</span>
                        <span class="stat-value">0/0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Consecutive Losses:</span>
                        <span class="stat-value">0/3</span>
                    </div>
                </div>
                <div id="tradeHistory" class="trade-list">
                    <div class="no-trades">No trades yet</div>
                </div>
            </div>
        </section>
        
        <!-- Trade History Panel (hidden by default) -->
        <section id="tradeHistoryPanel" class="trade-history-panel" style="display:none;">
            <div class="panel-header">
                <h3>Trade History</h3>
                <button id="closeTradeHistory" class="btn btn-small">Close</button>
            </div>
            <div class="panel-controls">
                <button id="exportTradeHistory" class="btn btn-small">Export CSV</button>
                <button id="clearTradeHistory" class="btn btn-small btn-danger">Clear History</button>
            </div>
            <div class="trade-settings">
                <h4>Risk Settings</h4>
                <div class="setting-item">
                    <label for="maxTradesPerDay">Max Trades/Day:</label>
                    <input type="number" id="maxTradesPerDay" min="1" max="20" value="5">
                </div>
                <div class="setting-item">
                    <label for="tradeCooldown">Cooldown (min):</label>
                    <input type="number" id="tradeCooldown" min="1" max="60" value="5">
                </div>
                <div class="setting-item">
                    <label for="maxConsecutiveLosses">Stop After Losses:</label>
                    <input type="number" id="maxConsecutiveLosses" min="1" max="10" value="3">
                </div>
                <button id="resetEmergency" class="btn btn-small">Reset Emergency Stop</button>
            </div>
            <div id="fullTradeHistory" class="full-trade-list">
                <!-- Trade history items will be added here -->
            </div>
        </section>

        <!-- Status Bar -->
        <footer class="status-bar">
            <div class="status-item">
                <span class="status-label">Status:</span>
                <span id="systemStatus" class="status-value">Ready</span>
            </div>
            <div class="status-item">
                <span class="status-label">Signals Today:</span>
                <span id="signalsToday" class="status-value">0</span>
            </div>
        </footer>

        <!-- Logs Tab (Hidden by default) -->
        <section id="logsSection" class="logs-section" style="display:none;">
            <div class="logs-header">
                <h3>📈 Trade Log</h3>
                <button id="clearLogs" class="btn-small">Clear</button>
            </div>
            <div class="logs-container" id="logsContainer">
                <div class="no-logs">No signals logged yet</div>
            </div>
        </section>

        <!-- Tab Navigation -->
        <nav class="bottom-nav">
            <button class="nav-btn active" data-section="main">
                <span class="nav-icon">🎯</span>
                <span class="nav-text">Signals</span>
            </button>
            <button class="nav-btn" data-section="analysis">
                <span class="nav-icon">📊</span>
                <span class="nav-text">Analysis</span>
            </button>
            <button class="nav-btn" data-section="logs">
                <span class="nav-icon">📋</span>
                <span class="nav-text">Logs</span>
            </button>
        </nav>
    </div>

    <script src="popup.js"></script>
</body>
</html>